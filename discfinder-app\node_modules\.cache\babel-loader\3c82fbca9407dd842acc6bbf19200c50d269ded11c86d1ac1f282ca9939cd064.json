{"ast": null, "code": "import { version } from './version';\nexport const DEFAULT_VERSION = `realtime-js/${version}`;\nexport const VSN = '1.0.0';\nexport const VERSION = version;\nexport const DEFAULT_TIMEOUT = 10000;\nexport const WS_CLOSE_NORMAL = 1000;\nexport var SOCKET_STATES;\n(function (SOCKET_STATES) {\n  SOCKET_STATES[SOCKET_STATES[\"connecting\"] = 0] = \"connecting\";\n  SOCKET_STATES[SOCKET_STATES[\"open\"] = 1] = \"open\";\n  SOCKET_STATES[SOCKET_STATES[\"closing\"] = 2] = \"closing\";\n  SOCKET_STATES[SOCKET_STATES[\"closed\"] = 3] = \"closed\";\n})(SOCKET_STATES || (SOCKET_STATES = {}));\nexport var CHANNEL_STATES;\n(function (CHANNEL_STATES) {\n  CHANNEL_STATES[\"closed\"] = \"closed\";\n  CHANNEL_STATES[\"errored\"] = \"errored\";\n  CHANNEL_STATES[\"joined\"] = \"joined\";\n  CHANNEL_STATES[\"joining\"] = \"joining\";\n  CHANNEL_STATES[\"leaving\"] = \"leaving\";\n})(CHANNEL_STATES || (CHANNEL_STATES = {}));\nexport var CHANNEL_EVENTS;\n(function (CHANNEL_EVENTS) {\n  CHANNEL_EVENTS[\"close\"] = \"phx_close\";\n  CHANNEL_EVENTS[\"error\"] = \"phx_error\";\n  CHANNEL_EVENTS[\"join\"] = \"phx_join\";\n  CHANNEL_EVENTS[\"reply\"] = \"phx_reply\";\n  CHANNEL_EVENTS[\"leave\"] = \"phx_leave\";\n  CHANNEL_EVENTS[\"access_token\"] = \"access_token\";\n})(CHANNEL_EVENTS || (CHANNEL_EVENTS = {}));\nexport var TRANSPORTS;\n(function (TRANSPORTS) {\n  TRANSPORTS[\"websocket\"] = \"websocket\";\n})(TRANSPORTS || (TRANSPORTS = {}));\nexport var CONNECTION_STATE;\n(function (CONNECTION_STATE) {\n  CONNECTION_STATE[\"Connecting\"] = \"connecting\";\n  CONNECTION_STATE[\"Open\"] = \"open\";\n  CONNECTION_STATE[\"Closing\"] = \"closing\";\n  CONNECTION_STATE[\"Closed\"] = \"closed\";\n})(CONNECTION_STATE || (CONNECTION_STATE = {}));", "map": {"version": 3, "names": ["version", "DEFAULT_VERSION", "VSN", "VERSION", "DEFAULT_TIMEOUT", "WS_CLOSE_NORMAL", "SOCKET_STATES", "CHANNEL_STATES", "CHANNEL_EVENTS", "TRANSPORTS", "CONNECTION_STATE"], "sources": ["C:\\Users\\<USER>\\node_modules\\@supabase\\realtime-js\\src\\lib\\constants.ts"], "sourcesContent": ["import { version } from './version'\n\nexport const DEFAULT_VERSION = `realtime-js/${version}`\nexport const VSN: string = '1.0.0'\n\nexport const VERSION = version\n\nexport const DEFAULT_TIMEOUT = 10000\n\nexport const WS_CLOSE_NORMAL = 1000\n\nexport enum SOCKET_STATES {\n  connecting = 0,\n  open = 1,\n  closing = 2,\n  closed = 3,\n}\n\nexport enum CHANNEL_STATES {\n  closed = 'closed',\n  errored = 'errored',\n  joined = 'joined',\n  joining = 'joining',\n  leaving = 'leaving',\n}\n\nexport enum CHANNEL_EVENTS {\n  close = 'phx_close',\n  error = 'phx_error',\n  join = 'phx_join',\n  reply = 'phx_reply',\n  leave = 'phx_leave',\n  access_token = 'access_token',\n}\n\nexport enum TRANSPORTS {\n  websocket = 'websocket',\n}\n\nexport enum CONNECTION_STATE {\n  Connecting = 'connecting',\n  Open = 'open',\n  Closing = 'closing',\n  Closed = 'closed',\n}\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,WAAW;AAEnC,OAAO,MAAMC,eAAe,GAAG,eAAeD,OAAO,EAAE;AACvD,OAAO,MAAME,GAAG,GAAW,OAAO;AAElC,OAAO,MAAMC,OAAO,GAAGH,OAAO;AAE9B,OAAO,MAAMI,eAAe,GAAG,KAAK;AAEpC,OAAO,MAAMC,eAAe,GAAG,IAAI;AAEnC,WAAYC,aAKX;AALD,WAAYA,aAAa;EACvBA,aAAA,CAAAA,aAAA,kCAAc;EACdA,aAAA,CAAAA,aAAA,sBAAQ;EACRA,aAAA,CAAAA,aAAA,4BAAW;EACXA,aAAA,CAAAA,aAAA,0BAAU;AACZ,CAAC,EALWA,aAAa,KAAbA,aAAa;AAOzB,WAAYC,cAMX;AAND,WAAYA,cAAc;EACxBA,cAAA,qBAAiB;EACjBA,cAAA,uBAAmB;EACnBA,cAAA,qBAAiB;EACjBA,cAAA,uBAAmB;EACnBA,cAAA,uBAAmB;AACrB,CAAC,EANWA,cAAc,KAAdA,cAAc;AAQ1B,WAAYC,cAOX;AAPD,WAAYA,cAAc;EACxBA,cAAA,uBAAmB;EACnBA,cAAA,uBAAmB;EACnBA,cAAA,qBAAiB;EACjBA,cAAA,uBAAmB;EACnBA,cAAA,uBAAmB;EACnBA,cAAA,iCAA6B;AAC/B,CAAC,EAPWA,cAAc,KAAdA,cAAc;AAS1B,WAAYC,UAEX;AAFD,WAAYA,UAAU;EACpBA,UAAA,2BAAuB;AACzB,CAAC,EAFWA,UAAU,KAAVA,UAAU;AAItB,WAAYC,gBAKX;AALD,WAAYA,gBAAgB;EAC1BA,gBAAA,6BAAyB;EACzBA,gBAAA,iBAAa;EACbA,gBAAA,uBAAmB;EACnBA,gBAAA,qBAAiB;AACnB,CAAC,EALWA,gBAAgB,KAAhBA,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}