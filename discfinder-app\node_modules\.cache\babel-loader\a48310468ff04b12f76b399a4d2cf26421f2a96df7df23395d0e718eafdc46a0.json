{"ast": null, "code": "/**\n * Helpers to convert the change Payload into native JS types.\n */\n// Adapted from epgsql (src/epgsql_binary.erl), this module licensed under\n// 3-clause BSD found here: https://raw.githubusercontent.com/epgsql/epgsql/devel/LICENSE\nexport var PostgresTypes;\n(function (PostgresTypes) {\n  PostgresTypes[\"abstime\"] = \"abstime\";\n  PostgresTypes[\"bool\"] = \"bool\";\n  PostgresTypes[\"date\"] = \"date\";\n  PostgresTypes[\"daterange\"] = \"daterange\";\n  PostgresTypes[\"float4\"] = \"float4\";\n  PostgresTypes[\"float8\"] = \"float8\";\n  PostgresTypes[\"int2\"] = \"int2\";\n  PostgresTypes[\"int4\"] = \"int4\";\n  PostgresTypes[\"int4range\"] = \"int4range\";\n  PostgresTypes[\"int8\"] = \"int8\";\n  PostgresTypes[\"int8range\"] = \"int8range\";\n  PostgresTypes[\"json\"] = \"json\";\n  PostgresTypes[\"jsonb\"] = \"jsonb\";\n  PostgresTypes[\"money\"] = \"money\";\n  PostgresTypes[\"numeric\"] = \"numeric\";\n  PostgresTypes[\"oid\"] = \"oid\";\n  PostgresTypes[\"reltime\"] = \"reltime\";\n  PostgresTypes[\"text\"] = \"text\";\n  PostgresTypes[\"time\"] = \"time\";\n  PostgresTypes[\"timestamp\"] = \"timestamp\";\n  PostgresTypes[\"timestamptz\"] = \"timestamptz\";\n  PostgresTypes[\"timetz\"] = \"timetz\";\n  PostgresTypes[\"tsrange\"] = \"tsrange\";\n  PostgresTypes[\"tstzrange\"] = \"tstzrange\";\n})(PostgresTypes || (PostgresTypes = {}));\n/**\n * Takes an array of columns and an object of string values then converts each string value\n * to its mapped type.\n *\n * @param {{name: String, type: String}[]} columns\n * @param {Object} record\n * @param {Object} options The map of various options that can be applied to the mapper\n * @param {Array} options.skipTypes The array of types that should not be converted\n *\n * @example convertChangeData([{name: 'first_name', type: 'text'}, {name: 'age', type: 'int4'}], {first_name: 'Paul', age:'33'}, {})\n * //=>{ first_name: 'Paul', age: 33 }\n */\nexport const convertChangeData = (columns, record, options = {}) => {\n  var _a;\n  const skipTypes = (_a = options.skipTypes) !== null && _a !== void 0 ? _a : [];\n  return Object.keys(record).reduce((acc, rec_key) => {\n    acc[rec_key] = convertColumn(rec_key, columns, record, skipTypes);\n    return acc;\n  }, {});\n};\n/**\n * Converts the value of an individual column.\n *\n * @param {String} columnName The column that you want to convert\n * @param {{name: String, type: String}[]} columns All of the columns\n * @param {Object} record The map of string values\n * @param {Array} skipTypes An array of types that should not be converted\n * @return {object} Useless information\n *\n * @example convertColumn('age', [{name: 'first_name', type: 'text'}, {name: 'age', type: 'int4'}], {first_name: 'Paul', age: '33'}, [])\n * //=> 33\n * @example convertColumn('age', [{name: 'first_name', type: 'text'}, {name: 'age', type: 'int4'}], {first_name: 'Paul', age: '33'}, ['int4'])\n * //=> \"33\"\n */\nexport const convertColumn = (columnName, columns, record, skipTypes) => {\n  const column = columns.find(x => x.name === columnName);\n  const colType = column === null || column === void 0 ? void 0 : column.type;\n  const value = record[columnName];\n  if (colType && !skipTypes.includes(colType)) {\n    return convertCell(colType, value);\n  }\n  return noop(value);\n};\n/**\n * If the value of the cell is `null`, returns null.\n * Otherwise converts the string value to the correct type.\n * @param {String} type A postgres column type\n * @param {String} value The cell value\n *\n * @example convertCell('bool', 't')\n * //=> true\n * @example convertCell('int8', '10')\n * //=> 10\n * @example convertCell('_int4', '{1,2,3,4}')\n * //=> [1,2,3,4]\n */\nexport const convertCell = (type, value) => {\n  // if data type is an array\n  if (type.charAt(0) === '_') {\n    const dataType = type.slice(1, type.length);\n    return toArray(value, dataType);\n  }\n  // If not null, convert to correct type.\n  switch (type) {\n    case PostgresTypes.bool:\n      return toBoolean(value);\n    case PostgresTypes.float4:\n    case PostgresTypes.float8:\n    case PostgresTypes.int2:\n    case PostgresTypes.int4:\n    case PostgresTypes.int8:\n    case PostgresTypes.numeric:\n    case PostgresTypes.oid:\n      return toNumber(value);\n    case PostgresTypes.json:\n    case PostgresTypes.jsonb:\n      return toJson(value);\n    case PostgresTypes.timestamp:\n      return toTimestampString(value);\n    // Format to be consistent with PostgREST\n    case PostgresTypes.abstime: // To allow users to cast it based on Timezone\n    case PostgresTypes.date: // To allow users to cast it based on Timezone\n    case PostgresTypes.daterange:\n    case PostgresTypes.int4range:\n    case PostgresTypes.int8range:\n    case PostgresTypes.money:\n    case PostgresTypes.reltime: // To allow users to cast it based on Timezone\n    case PostgresTypes.text:\n    case PostgresTypes.time: // To allow users to cast it based on Timezone\n    case PostgresTypes.timestamptz: // To allow users to cast it based on Timezone\n    case PostgresTypes.timetz: // To allow users to cast it based on Timezone\n    case PostgresTypes.tsrange:\n    case PostgresTypes.tstzrange:\n      return noop(value);\n    default:\n      // Return the value for remaining types\n      return noop(value);\n  }\n};\nconst noop = value => {\n  return value;\n};\nexport const toBoolean = value => {\n  switch (value) {\n    case 't':\n      return true;\n    case 'f':\n      return false;\n    default:\n      return value;\n  }\n};\nexport const toNumber = value => {\n  if (typeof value === 'string') {\n    const parsedValue = parseFloat(value);\n    if (!Number.isNaN(parsedValue)) {\n      return parsedValue;\n    }\n  }\n  return value;\n};\nexport const toJson = value => {\n  if (typeof value === 'string') {\n    try {\n      return JSON.parse(value);\n    } catch (error) {\n      console.log(`JSON parse error: ${error}`);\n      return value;\n    }\n  }\n  return value;\n};\n/**\n * Converts a Postgres Array into a native JS array\n *\n * @example toArray('{}', 'int4')\n * //=> []\n * @example toArray('{\"[2021-01-01,2021-12-31)\",\"(2021-01-01,2021-12-32]\"}', 'daterange')\n * //=> ['[2021-01-01,2021-12-31)', '(2021-01-01,2021-12-32]']\n * @example toArray([1,2,3,4], 'int4')\n * //=> [1,2,3,4]\n */\nexport const toArray = (value, type) => {\n  if (typeof value !== 'string') {\n    return value;\n  }\n  const lastIdx = value.length - 1;\n  const closeBrace = value[lastIdx];\n  const openBrace = value[0];\n  // Confirm value is a Postgres array by checking curly brackets\n  if (openBrace === '{' && closeBrace === '}') {\n    let arr;\n    const valTrim = value.slice(1, lastIdx);\n    // TODO: find a better solution to separate Postgres array data\n    try {\n      arr = JSON.parse('[' + valTrim + ']');\n    } catch (_) {\n      // WARNING: splitting on comma does not cover all edge cases\n      arr = valTrim ? valTrim.split(',') : [];\n    }\n    return arr.map(val => convertCell(type, val));\n  }\n  return value;\n};\n/**\n * Fixes timestamp to be ISO-8601. Swaps the space between the date and time for a 'T'\n * See https://github.com/supabase/supabase/issues/18\n *\n * @example toTimestampString('2019-09-10 00:00:00')\n * //=> '2019-09-10T00:00:00'\n */\nexport const toTimestampString = value => {\n  if (typeof value === 'string') {\n    return value.replace(' ', 'T');\n  }\n  return value;\n};\nexport const httpEndpointURL = socketUrl => {\n  let url = socketUrl;\n  url = url.replace(/^ws/i, 'http');\n  url = url.replace(/(\\/socket\\/websocket|\\/socket|\\/websocket)\\/?$/i, '');\n  return url.replace(/\\/+$/, '');\n};", "map": {"version": 3, "names": ["PostgresTypes", "convertChangeData", "columns", "record", "options", "skipTypes", "_a", "Object", "keys", "reduce", "acc", "rec_key", "convertColumn", "columnName", "column", "find", "x", "name", "colType", "type", "value", "includes", "convertCell", "noop", "char<PERSON>t", "dataType", "slice", "length", "toArray", "bool", "toBoolean", "float4", "float8", "int2", "int4", "int8", "numeric", "oid", "toNumber", "json", "jsonb", "to<PERSON><PERSON>", "timestamp", "toTimestampString", "abstime", "date", "daterange", "int4range", "int8range", "money", "reltime", "text", "time", "timestamptz", "timetz", "tsrange", "tstzrange", "parsedValue", "parseFloat", "Number", "isNaN", "JSON", "parse", "error", "console", "log", "lastIdx", "closeBrace", "openBrace", "arr", "valTrim", "_", "split", "map", "val", "replace", "httpEndpointURL", "socketUrl", "url"], "sources": ["C:\\Users\\<USER>\\node_modules\\@supabase\\realtime-js\\src\\lib\\transformers.ts"], "sourcesContent": ["/**\n * Helpers to convert the change Payload into native JS types.\n */\n\n// Adapted from epgsql (src/epgsql_binary.erl), this module licensed under\n// 3-clause BSD found here: https://raw.githubusercontent.com/epgsql/epgsql/devel/LICENSE\n\nexport enum PostgresTypes {\n  abstime = 'abstime',\n  bool = 'bool',\n  date = 'date',\n  daterange = 'daterange',\n  float4 = 'float4',\n  float8 = 'float8',\n  int2 = 'int2',\n  int4 = 'int4',\n  int4range = 'int4range',\n  int8 = 'int8',\n  int8range = 'int8range',\n  json = 'json',\n  jsonb = 'jsonb',\n  money = 'money',\n  numeric = 'numeric',\n  oid = 'oid',\n  reltime = 'reltime',\n  text = 'text',\n  time = 'time',\n  timestamp = 'timestamp',\n  timestamptz = 'timestamptz',\n  timetz = 'timetz',\n  tsrange = 'tsrange',\n  tstzrange = 'tstzrange',\n}\n\ntype Columns = {\n  name: string // the column name. eg: \"user_id\"\n  type: string // the column type. eg: \"uuid\"\n  flags?: string[] // any special flags for the column. eg: [\"key\"]\n  type_modifier?: number // the type modifier. eg: **********\n}[]\n\ntype BaseValue = null | string | number | boolean\ntype RecordValue = BaseValue | BaseValue[]\n\ntype Record = {\n  [key: string]: RecordValue\n}\n\n/**\n * Takes an array of columns and an object of string values then converts each string value\n * to its mapped type.\n *\n * @param {{name: String, type: String}[]} columns\n * @param {Object} record\n * @param {Object} options The map of various options that can be applied to the mapper\n * @param {Array} options.skipTypes The array of types that should not be converted\n *\n * @example convertChangeData([{name: 'first_name', type: 'text'}, {name: 'age', type: 'int4'}], {first_name: 'Paul', age:'33'}, {})\n * //=>{ first_name: 'Paul', age: 33 }\n */\nexport const convertChangeData = (\n  columns: Columns,\n  record: Record,\n  options: { skipTypes?: string[] } = {}\n): Record => {\n  const skipTypes = options.skipTypes ?? []\n\n  return Object.keys(record).reduce((acc, rec_key) => {\n    acc[rec_key] = convertColumn(rec_key, columns, record, skipTypes)\n    return acc\n  }, {} as Record)\n}\n\n/**\n * Converts the value of an individual column.\n *\n * @param {String} columnName The column that you want to convert\n * @param {{name: String, type: String}[]} columns All of the columns\n * @param {Object} record The map of string values\n * @param {Array} skipTypes An array of types that should not be converted\n * @return {object} Useless information\n *\n * @example convertColumn('age', [{name: 'first_name', type: 'text'}, {name: 'age', type: 'int4'}], {first_name: 'Paul', age: '33'}, [])\n * //=> 33\n * @example convertColumn('age', [{name: 'first_name', type: 'text'}, {name: 'age', type: 'int4'}], {first_name: 'Paul', age: '33'}, ['int4'])\n * //=> \"33\"\n */\nexport const convertColumn = (\n  columnName: string,\n  columns: Columns,\n  record: Record,\n  skipTypes: string[]\n): RecordValue => {\n  const column = columns.find((x) => x.name === columnName)\n  const colType = column?.type\n  const value = record[columnName]\n\n  if (colType && !skipTypes.includes(colType)) {\n    return convertCell(colType, value)\n  }\n\n  return noop(value)\n}\n\n/**\n * If the value of the cell is `null`, returns null.\n * Otherwise converts the string value to the correct type.\n * @param {String} type A postgres column type\n * @param {String} value The cell value\n *\n * @example convertCell('bool', 't')\n * //=> true\n * @example convertCell('int8', '10')\n * //=> 10\n * @example convertCell('_int4', '{1,2,3,4}')\n * //=> [1,2,3,4]\n */\nexport const convertCell = (type: string, value: RecordValue): RecordValue => {\n  // if data type is an array\n  if (type.charAt(0) === '_') {\n    const dataType = type.slice(1, type.length)\n    return toArray(value, dataType)\n  }\n\n  // If not null, convert to correct type.\n  switch (type) {\n    case PostgresTypes.bool:\n      return toBoolean(value)\n    case PostgresTypes.float4:\n    case PostgresTypes.float8:\n    case PostgresTypes.int2:\n    case PostgresTypes.int4:\n    case PostgresTypes.int8:\n    case PostgresTypes.numeric:\n    case PostgresTypes.oid:\n      return toNumber(value)\n    case PostgresTypes.json:\n    case PostgresTypes.jsonb:\n      return toJson(value)\n    case PostgresTypes.timestamp:\n      return toTimestampString(value) // Format to be consistent with PostgREST\n    case PostgresTypes.abstime: // To allow users to cast it based on Timezone\n    case PostgresTypes.date: // To allow users to cast it based on Timezone\n    case PostgresTypes.daterange:\n    case PostgresTypes.int4range:\n    case PostgresTypes.int8range:\n    case PostgresTypes.money:\n    case PostgresTypes.reltime: // To allow users to cast it based on Timezone\n    case PostgresTypes.text:\n    case PostgresTypes.time: // To allow users to cast it based on Timezone\n    case PostgresTypes.timestamptz: // To allow users to cast it based on Timezone\n    case PostgresTypes.timetz: // To allow users to cast it based on Timezone\n    case PostgresTypes.tsrange:\n    case PostgresTypes.tstzrange:\n      return noop(value)\n    default:\n      // Return the value for remaining types\n      return noop(value)\n  }\n}\n\nconst noop = (value: RecordValue): RecordValue => {\n  return value\n}\nexport const toBoolean = (value: RecordValue): RecordValue => {\n  switch (value) {\n    case 't':\n      return true\n    case 'f':\n      return false\n    default:\n      return value\n  }\n}\nexport const toNumber = (value: RecordValue): RecordValue => {\n  if (typeof value === 'string') {\n    const parsedValue = parseFloat(value)\n    if (!Number.isNaN(parsedValue)) {\n      return parsedValue\n    }\n  }\n  return value\n}\nexport const toJson = (value: RecordValue): RecordValue => {\n  if (typeof value === 'string') {\n    try {\n      return JSON.parse(value)\n    } catch (error) {\n      console.log(`JSON parse error: ${error}`)\n      return value\n    }\n  }\n  return value\n}\n\n/**\n * Converts a Postgres Array into a native JS array\n *\n * @example toArray('{}', 'int4')\n * //=> []\n * @example toArray('{\"[2021-01-01,2021-12-31)\",\"(2021-01-01,2021-12-32]\"}', 'daterange')\n * //=> ['[2021-01-01,2021-12-31)', '(2021-01-01,2021-12-32]']\n * @example toArray([1,2,3,4], 'int4')\n * //=> [1,2,3,4]\n */\nexport const toArray = (value: RecordValue, type: string): RecordValue => {\n  if (typeof value !== 'string') {\n    return value\n  }\n\n  const lastIdx = value.length - 1\n  const closeBrace = value[lastIdx]\n  const openBrace = value[0]\n\n  // Confirm value is a Postgres array by checking curly brackets\n  if (openBrace === '{' && closeBrace === '}') {\n    let arr\n    const valTrim = value.slice(1, lastIdx)\n\n    // TODO: find a better solution to separate Postgres array data\n    try {\n      arr = JSON.parse('[' + valTrim + ']')\n    } catch (_) {\n      // WARNING: splitting on comma does not cover all edge cases\n      arr = valTrim ? valTrim.split(',') : []\n    }\n\n    return arr.map((val: BaseValue) => convertCell(type, val))\n  }\n\n  return value\n}\n\n/**\n * Fixes timestamp to be ISO-8601. Swaps the space between the date and time for a 'T'\n * See https://github.com/supabase/supabase/issues/18\n *\n * @example toTimestampString('2019-09-10 00:00:00')\n * //=> '2019-09-10T00:00:00'\n */\nexport const toTimestampString = (value: RecordValue): RecordValue => {\n  if (typeof value === 'string') {\n    return value.replace(' ', 'T')\n  }\n\n  return value\n}\n\nexport const httpEndpointURL = (socketUrl: string): string => {\n  let url = socketUrl\n  url = url.replace(/^ws/i, 'http')\n  url = url.replace(/(\\/socket\\/websocket|\\/socket|\\/websocket)\\/?$/i, '')\n  return url.replace(/\\/+$/, '')\n}\n"], "mappings": "AAAA;;;AAIA;AACA;AAEA,WAAYA,aAyBX;AAzBD,WAAYA,aAAa;EACvBA,aAAA,uBAAmB;EACnBA,aAAA,iBAAa;EACbA,aAAA,iBAAa;EACbA,aAAA,2BAAuB;EACvBA,aAAA,qBAAiB;EACjBA,aAAA,qBAAiB;EACjBA,aAAA,iBAAa;EACbA,aAAA,iBAAa;EACbA,aAAA,2BAAuB;EACvBA,aAAA,iBAAa;EACbA,aAAA,2BAAuB;EACvBA,aAAA,iBAAa;EACbA,aAAA,mBAAe;EACfA,aAAA,mBAAe;EACfA,aAAA,uBAAmB;EACnBA,aAAA,eAAW;EACXA,aAAA,uBAAmB;EACnBA,aAAA,iBAAa;EACbA,aAAA,iBAAa;EACbA,aAAA,2BAAuB;EACvBA,aAAA,+BAA2B;EAC3BA,aAAA,qBAAiB;EACjBA,aAAA,uBAAmB;EACnBA,aAAA,2BAAuB;AACzB,CAAC,EAzBWA,aAAa,KAAbA,aAAa;AAyCzB;;;;;;;;;;;;AAYA,OAAO,MAAMC,iBAAiB,GAAGA,CAC/BC,OAAgB,EAChBC,MAAc,EACdC,OAAA,GAAoC,EAAE,KAC5B;;EACV,MAAMC,SAAS,GAAG,CAAAC,EAAA,GAAAF,OAAO,CAACC,SAAS,cAAAC,EAAA,cAAAA,EAAA,GAAI,EAAE;EAEzC,OAAOC,MAAM,CAACC,IAAI,CAACL,MAAM,CAAC,CAACM,MAAM,CAAC,CAACC,GAAG,EAAEC,OAAO,KAAI;IACjDD,GAAG,CAACC,OAAO,CAAC,GAAGC,aAAa,CAACD,OAAO,EAAET,OAAO,EAAEC,MAAM,EAAEE,SAAS,CAAC;IACjE,OAAOK,GAAG;EACZ,CAAC,EAAE,EAAY,CAAC;AAClB,CAAC;AAED;;;;;;;;;;;;;;AAcA,OAAO,MAAME,aAAa,GAAGA,CAC3BC,UAAkB,EAClBX,OAAgB,EAChBC,MAAc,EACdE,SAAmB,KACJ;EACf,MAAMS,MAAM,GAAGZ,OAAO,CAACa,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACC,IAAI,KAAKJ,UAAU,CAAC;EACzD,MAAMK,OAAO,GAAGJ,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEK,IAAI;EAC5B,MAAMC,KAAK,GAAGjB,MAAM,CAACU,UAAU,CAAC;EAEhC,IAAIK,OAAO,IAAI,CAACb,SAAS,CAACgB,QAAQ,CAACH,OAAO,CAAC,EAAE;IAC3C,OAAOI,WAAW,CAACJ,OAAO,EAAEE,KAAK,CAAC;EACpC;EAEA,OAAOG,IAAI,CAACH,KAAK,CAAC;AACpB,CAAC;AAED;;;;;;;;;;;;;AAaA,OAAO,MAAME,WAAW,GAAGA,CAACH,IAAY,EAAEC,KAAkB,KAAiB;EAC3E;EACA,IAAID,IAAI,CAACK,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;IAC1B,MAAMC,QAAQ,GAAGN,IAAI,CAACO,KAAK,CAAC,CAAC,EAAEP,IAAI,CAACQ,MAAM,CAAC;IAC3C,OAAOC,OAAO,CAACR,KAAK,EAAEK,QAAQ,CAAC;EACjC;EAEA;EACA,QAAQN,IAAI;IACV,KAAKnB,aAAa,CAAC6B,IAAI;MACrB,OAAOC,SAAS,CAACV,KAAK,CAAC;IACzB,KAAKpB,aAAa,CAAC+B,MAAM;IACzB,KAAK/B,aAAa,CAACgC,MAAM;IACzB,KAAKhC,aAAa,CAACiC,IAAI;IACvB,KAAKjC,aAAa,CAACkC,IAAI;IACvB,KAAKlC,aAAa,CAACmC,IAAI;IACvB,KAAKnC,aAAa,CAACoC,OAAO;IAC1B,KAAKpC,aAAa,CAACqC,GAAG;MACpB,OAAOC,QAAQ,CAAClB,KAAK,CAAC;IACxB,KAAKpB,aAAa,CAACuC,IAAI;IACvB,KAAKvC,aAAa,CAACwC,KAAK;MACtB,OAAOC,MAAM,CAACrB,KAAK,CAAC;IACtB,KAAKpB,aAAa,CAAC0C,SAAS;MAC1B,OAAOC,iBAAiB,CAACvB,KAAK,CAAC;IAAC;IAClC,KAAKpB,aAAa,CAAC4C,OAAO,CAAC,CAAC;IAC5B,KAAK5C,aAAa,CAAC6C,IAAI,CAAC,CAAC;IACzB,KAAK7C,aAAa,CAAC8C,SAAS;IAC5B,KAAK9C,aAAa,CAAC+C,SAAS;IAC5B,KAAK/C,aAAa,CAACgD,SAAS;IAC5B,KAAKhD,aAAa,CAACiD,KAAK;IACxB,KAAKjD,aAAa,CAACkD,OAAO,CAAC,CAAC;IAC5B,KAAKlD,aAAa,CAACmD,IAAI;IACvB,KAAKnD,aAAa,CAACoD,IAAI,CAAC,CAAC;IACzB,KAAKpD,aAAa,CAACqD,WAAW,CAAC,CAAC;IAChC,KAAKrD,aAAa,CAACsD,MAAM,CAAC,CAAC;IAC3B,KAAKtD,aAAa,CAACuD,OAAO;IAC1B,KAAKvD,aAAa,CAACwD,SAAS;MAC1B,OAAOjC,IAAI,CAACH,KAAK,CAAC;IACpB;MACE;MACA,OAAOG,IAAI,CAACH,KAAK,CAAC;EACtB;AACF,CAAC;AAED,MAAMG,IAAI,GAAIH,KAAkB,IAAiB;EAC/C,OAAOA,KAAK;AACd,CAAC;AACD,OAAO,MAAMU,SAAS,GAAIV,KAAkB,IAAiB;EAC3D,QAAQA,KAAK;IACX,KAAK,GAAG;MACN,OAAO,IAAI;IACb,KAAK,GAAG;MACN,OAAO,KAAK;IACd;MACE,OAAOA,KAAK;EAChB;AACF,CAAC;AACD,OAAO,MAAMkB,QAAQ,GAAIlB,KAAkB,IAAiB;EAC1D,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;IAC7B,MAAMqC,WAAW,GAAGC,UAAU,CAACtC,KAAK,CAAC;IACrC,IAAI,CAACuC,MAAM,CAACC,KAAK,CAACH,WAAW,CAAC,EAAE;MAC9B,OAAOA,WAAW;IACpB;EACF;EACA,OAAOrC,KAAK;AACd,CAAC;AACD,OAAO,MAAMqB,MAAM,GAAIrB,KAAkB,IAAiB;EACxD,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;IAC7B,IAAI;MACF,OAAOyC,IAAI,CAACC,KAAK,CAAC1C,KAAK,CAAC;IAC1B,CAAC,CAAC,OAAO2C,KAAK,EAAE;MACdC,OAAO,CAACC,GAAG,CAAC,qBAAqBF,KAAK,EAAE,CAAC;MACzC,OAAO3C,KAAK;IACd;EACF;EACA,OAAOA,KAAK;AACd,CAAC;AAED;;;;;;;;;;AAUA,OAAO,MAAMQ,OAAO,GAAGA,CAACR,KAAkB,EAAED,IAAY,KAAiB;EACvE,IAAI,OAAOC,KAAK,KAAK,QAAQ,EAAE;IAC7B,OAAOA,KAAK;EACd;EAEA,MAAM8C,OAAO,GAAG9C,KAAK,CAACO,MAAM,GAAG,CAAC;EAChC,MAAMwC,UAAU,GAAG/C,KAAK,CAAC8C,OAAO,CAAC;EACjC,MAAME,SAAS,GAAGhD,KAAK,CAAC,CAAC,CAAC;EAE1B;EACA,IAAIgD,SAAS,KAAK,GAAG,IAAID,UAAU,KAAK,GAAG,EAAE;IAC3C,IAAIE,GAAG;IACP,MAAMC,OAAO,GAAGlD,KAAK,CAACM,KAAK,CAAC,CAAC,EAAEwC,OAAO,CAAC;IAEvC;IACA,IAAI;MACFG,GAAG,GAAGR,IAAI,CAACC,KAAK,CAAC,GAAG,GAAGQ,OAAO,GAAG,GAAG,CAAC;IACvC,CAAC,CAAC,OAAOC,CAAC,EAAE;MACV;MACAF,GAAG,GAAGC,OAAO,GAAGA,OAAO,CAACE,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE;IACzC;IAEA,OAAOH,GAAG,CAACI,GAAG,CAAEC,GAAc,IAAKpD,WAAW,CAACH,IAAI,EAAEuD,GAAG,CAAC,CAAC;EAC5D;EAEA,OAAOtD,KAAK;AACd,CAAC;AAED;;;;;;;AAOA,OAAO,MAAMuB,iBAAiB,GAAIvB,KAAkB,IAAiB;EACnE,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;IAC7B,OAAOA,KAAK,CAACuD,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;EAChC;EAEA,OAAOvD,KAAK;AACd,CAAC;AAED,OAAO,MAAMwD,eAAe,GAAIC,SAAiB,IAAY;EAC3D,IAAIC,GAAG,GAAGD,SAAS;EACnBC,GAAG,GAAGA,GAAG,CAACH,OAAO,CAAC,MAAM,EAAE,MAAM,CAAC;EACjCG,GAAG,GAAGA,GAAG,CAACH,OAAO,CAAC,iDAAiD,EAAE,EAAE,CAAC;EACxE,OAAOG,GAAG,CAACH,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC;AAChC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}