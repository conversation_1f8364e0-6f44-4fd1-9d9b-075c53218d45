{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\lostandfound\\\\discfinder-app\\\\src\\\\App.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$(),\n  _s3 = $RefreshSig$(),\n  _s4 = $RefreshSig$(),\n  _s5 = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { discService, imageService } from './lib/supabase';\nimport { AuthProvider, useAuth } from './contexts/AuthContext';\nimport { ImageUpload } from './components/ImageUpload';\nimport { ReturnStatusManager } from './components/ReturnStatusManager';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction AppContent() {\n  _s();\n  const [currentPage, setCurrentPage] = useState('home');\n  const {\n    user,\n    userRole,\n    signOut,\n    loading\n  } = useAuth();\n  const renderPage = () => {\n    switch (currentPage) {\n      case 'home':\n        return /*#__PURE__*/_jsxDEV(Home, {\n          onNavigate: setCurrentPage\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 16,\n          columnNumber: 16\n        }, this);\n      case 'report-found':\n        return /*#__PURE__*/_jsxDEV(ReportFound, {\n          onNavigate: setCurrentPage\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 18,\n          columnNumber: 16\n        }, this);\n      case 'search-lost':\n        return /*#__PURE__*/_jsxDEV(SearchLost, {\n          onNavigate: setCurrentPage\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 20,\n          columnNumber: 16\n        }, this);\n      case 'login':\n        return /*#__PURE__*/_jsxDEV(Login, {\n          onNavigate: setCurrentPage\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 22,\n          columnNumber: 16\n        }, this);\n      case 'admin':\n        return /*#__PURE__*/_jsxDEV(AdminDashboard, {\n          onNavigate: setCurrentPage\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 24,\n          columnNumber: 16\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(Home, {\n          onNavigate: setCurrentPage\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 26,\n          columnNumber: 16\n        }, this);\n    }\n  };\n  const handleSignOut = async () => {\n    await signOut();\n    setCurrentPage('home');\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"app\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"loading-spinner\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 39,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Loading...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 40,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            fontSize: '0.8rem',\n            color: '#666',\n            marginTop: '1rem'\n          },\n          children: \"If this takes too long, check the browser console for errors\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 41,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 38,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 37,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"app\",\n    children: [/*#__PURE__*/_jsxDEV(\"nav\", {\n      className: \"navbar\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"nav-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"logo\",\n          onClick: () => setCurrentPage('home'),\n          style: {\n            cursor: 'pointer'\n          },\n          children: \"DiscFinder\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 53,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"nav-buttons\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"nav-button\",\n            onClick: () => setCurrentPage('report-found'),\n            children: \"Report Found\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 57,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"nav-button\",\n            onClick: () => setCurrentPage('search-lost'),\n            children: \"Search Lost\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 60,\n            columnNumber: 13\n          }, this), user ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"user-menu\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"user-info\",\n              children: [user.email, \" (\", userRole, \")\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 66,\n              columnNumber: 17\n            }, this), userRole === 'admin' && /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"nav-button\",\n              onClick: () => setCurrentPage('admin'),\n              children: \"Admin\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 70,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"nav-button\",\n              onClick: handleSignOut,\n              children: \"Sign Out\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 74,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 65,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"nav-button primary\",\n            onClick: () => setCurrentPage('login'),\n            children: \"Sign In\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 79,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 56,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 52,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 51,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n      className: \"main-container\",\n      children: renderPage()\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 87,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 50,\n    columnNumber: 5\n  }, this);\n}\n_s(AppContent, \"/kJIu7Z5Ok/vEH6yLgQ1dcDyKKY=\", false, function () {\n  return [useAuth];\n});\n_c = AppContent;\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(AuthProvider, {\n    children: /*#__PURE__*/_jsxDEV(AppContent, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 97,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 96,\n    columnNumber: 5\n  }, this);\n}\n_c2 = App;\nfunction AdminDashboard({\n  onNavigate\n}) {\n  _s2();\n  const {\n    userRole\n  } = useAuth();\n  const [allDiscs, setAllDiscs] = useState([]);\n  const [isLoading, setIsLoading] = useState(false);\n  const [filter, setFilter] = useState('All');\n  const loadAllDiscs = async () => {\n    setIsLoading(true);\n    try {\n      const {\n        data,\n        error\n      } = await discService.getAdminFoundDiscs();\n      if (error) {\n        console.error('Error loading admin discs:', error);\n      } else {\n        console.log('Admin discs loaded:', data);\n        setAllDiscs(data || []);\n      }\n    } catch (error) {\n      console.error('Error loading admin discs:', error);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n  React.useEffect(() => {\n    loadAllDiscs();\n  }, []);\n  const handleReturnStatusUpdate = (discId, newStatus) => {\n    setAllDiscs(prev => prev.map(disc => disc.id === discId ? {\n      ...disc,\n      return_status: newStatus,\n      returned_at: new Date().toISOString()\n    } : disc));\n  };\n  const filteredDiscs = filter === 'All' ? allDiscs : allDiscs.filter(disc => disc.return_status === filter);\n  const statusCounts = allDiscs.reduce((counts, disc) => {\n    const status = disc.return_status || 'Found';\n    counts[status] = (counts[status] || 0) + 1;\n    return counts;\n  }, {});\n\n  // Redirect if not admin\n  if (userRole !== 'admin') {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"form-container\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"back-button\",\n          onClick: () => onNavigate('home'),\n          children: \"\\u2190 Back to Home\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 156,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n          children: \"Access Denied\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 159,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"You need admin privileges to access this page.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 160,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 155,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 154,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"form-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"form-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"back-button\",\n        onClick: () => onNavigate('home'),\n        children: \"\\u2190 Back to Home\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 169,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n        children: \"Admin Dashboard\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 172,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Manage all found discs and their return status.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 173,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 168,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"status-summary\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        children: \"Status Summary\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 178,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"status-counts\",\n        children: Object.entries(statusCounts).map(([status, count]) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"status-count\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"count\",\n            children: count\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 182,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"status\",\n            children: status\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 183,\n            columnNumber: 15\n          }, this)]\n        }, status, true, {\n          fileName: _jsxFileName,\n          lineNumber: 181,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 179,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 177,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"filter-controls\",\n      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n        htmlFor: \"status-filter\",\n        children: \"Filter by Status:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 191,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n        id: \"status-filter\",\n        value: filter,\n        onChange: e => setFilter(e.target.value),\n        children: [/*#__PURE__*/_jsxDEV(\"option\", {\n          value: \"All\",\n          children: \"All Statuses\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 197,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n          value: \"Found\",\n          children: \"Found\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 198,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n          value: \"Returned to Owner\",\n          children: \"Returned to Owner\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 199,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n          value: \"Donated\",\n          children: \"Donated\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 200,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n          value: \"Sold\",\n          children: \"Sold\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 201,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n          value: \"Trashed\",\n          children: \"Trashed\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 202,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 192,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 190,\n      columnNumber: 7\n    }, this), isLoading ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"loading-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading-spinner\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 209,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Loading discs...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 210,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 208,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"admin-disc-grid\",\n      children: filteredDiscs.length === 0 ? /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"No discs found for the selected filter.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 215,\n        columnNumber: 13\n      }, this) : filteredDiscs.map(disc => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"admin-disc-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"disc-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            children: [disc.brand, \" \", disc.mold || 'Unknown Mold']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 220,\n            columnNumber: 19\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"disc-type\",\n            children: disc.disc_type || 'Unknown Type'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 221,\n            columnNumber: 19\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 219,\n          columnNumber: 17\n        }, this), /*#__PURE__*/_jsxDEV(ReturnStatusManager, {\n          discId: disc.id,\n          currentStatus: disc.return_status || 'Found',\n          onStatusUpdated: newStatus => handleReturnStatusUpdate(disc.id, newStatus),\n          disabled: false\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 224,\n          columnNumber: 17\n        }, this), disc.image_urls && disc.image_urls.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"disc-images\",\n          children: disc.image_urls.slice(0, 2).map((imageUrl, index) => /*#__PURE__*/_jsxDEV(\"img\", {\n            src: imageUrl,\n            alt: `${disc.brand} ${disc.mold || 'disc'} ${index + 1}`,\n            className: \"disc-image\",\n            onError: e => {\n              e.target.style.display = 'none';\n            }\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 234,\n            columnNumber: 23\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 232,\n          columnNumber: 19\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"disc-details\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"detail-row\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"label\",\n              children: \"Color:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 249,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"value\",\n              children: disc.color\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 250,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 248,\n            columnNumber: 19\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"detail-row\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"label\",\n              children: \"Location Found:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 253,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"value\",\n              children: disc.location_found\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 254,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 252,\n            columnNumber: 19\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"detail-row\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"label\",\n              children: \"Found Date:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 257,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"value\",\n              children: new Date(disc.found_date).toLocaleDateString()\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 258,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 256,\n            columnNumber: 19\n          }, this), disc.returned_at && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"detail-row\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"label\",\n              children: \"Returned Date:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 262,\n              columnNumber: 23\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"value\",\n              children: new Date(disc.returned_at).toLocaleDateString()\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 263,\n              columnNumber: 23\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 261,\n            columnNumber: 21\n          }, this), disc.returned_notes && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"detail-row\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"label\",\n              children: \"Notes:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 268,\n              columnNumber: 23\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"value\",\n              children: disc.returned_notes\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 269,\n              columnNumber: 23\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 267,\n            columnNumber: 21\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 247,\n          columnNumber: 17\n        }, this)]\n      }, disc.id, true, {\n        fileName: _jsxFileName,\n        lineNumber: 218,\n        columnNumber: 15\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 213,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 167,\n    columnNumber: 5\n  }, this);\n}\n_s2(AdminDashboard, \"KRMzDO8sAsVTVi7M+/wMOCWSHis=\", false, function () {\n  return [useAuth];\n});\n_c3 = AdminDashboard;\nfunction Home({\n  onNavigate\n}) {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"hero\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        children: \"Lost Your Disc?\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 286,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"DiscFinder helps disc golf players reunite with their lost discs. Report found discs or search for your lost ones in our community database.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 287,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"hero-buttons\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"hero-button primary\",\n          onClick: () => onNavigate('report-found'),\n          children: \"Report Found Disc\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 293,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"hero-button secondary\",\n          onClick: () => onNavigate('search-lost'),\n          children: \"Search Lost Discs\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 296,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 292,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 285,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"features\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"feature-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"feature-icon\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            children: \"\\uD83D\\uDD0D\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 305,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 304,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Smart Matching\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 307,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Our intelligent system matches found and lost discs based on brand, model, color, and location.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 308,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 303,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"feature-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"feature-icon\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            children: \"\\uD83D\\uDCCD\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 315,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 314,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Location Based\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 317,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Find discs near where you lost them with our location-based search and matching.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 318,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 313,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"feature-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"feature-icon\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            children: \"\\uD83D\\uDCAC\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 325,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 324,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Easy Communication\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 327,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Connect directly with finders and owners through our secure messaging system.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 328,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 323,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 302,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"stats\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stats-grid\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-number\",\n            children: \"500+\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 337,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-label\",\n            children: \"Discs Reunited\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 338,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 336,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-number\",\n            children: \"1,200+\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 341,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-label\",\n            children: \"Active Users\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 342,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 340,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-number\",\n            children: \"95%\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 345,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-label\",\n            children: \"Success Rate\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 346,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 344,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 335,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 334,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"cta\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"Join the Community\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 352,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Create an account to report found discs, search for lost ones, and help fellow disc golfers.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 353,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"cta-button\",\n        onClick: () => onNavigate('login'),\n        children: \"Sign Up Now\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 356,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 351,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 284,\n    columnNumber: 5\n  }, this);\n}\n_c4 = Home;\nfunction ReportFound({\n  onNavigate\n}) {\n  _s3();\n  const {\n    user,\n    isGuest,\n    userRole\n  } = useAuth();\n  const [formData, setFormData] = useState({\n    brand: '',\n    mold: '',\n    discType: '',\n    color: '',\n    weight: '',\n    condition: '',\n    plasticType: '',\n    stampText: '',\n    phoneNumber: '',\n    nameOnDisc: '',\n    locationFound: '',\n    foundDate: '',\n    description: ''\n  });\n  const [selectedImages, setSelectedImages] = useState([]);\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [submitMessage, setSubmitMessage] = useState('');\n\n  // Require authentication to report found discs\n  if (isGuest) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"page-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"page-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"back-button\",\n          onClick: () => onNavigate('home'),\n          children: \"\\u2190 Back to Home\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 390,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n          children: \"Report a Found Disc\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 393,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"You must be signed in to report found discs.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 394,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 389,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"auth-required\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Authentication Required\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 397,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Please sign in or create an account to report found discs. This helps us maintain data quality and allows disc owners to contact you.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 398,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"button primary\",\n          onClick: () => onNavigate('login'),\n          children: \"Sign In / Sign Up\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 399,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 396,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 388,\n      columnNumber: 7\n    }, this);\n  }\n  const handleInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setIsSubmitting(true);\n    setSubmitMessage('');\n    try {\n      console.log('Form submission started');\n      console.log('User:', user);\n      console.log('Selected images:', selectedImages.length);\n\n      // Check if user is authenticated for image upload\n      if (selectedImages.length > 0 && !user) {\n        setSubmitMessage('Error: You must be signed in to upload images');\n        return;\n      }\n\n      // Test connection first\n      const {\n        connected\n      } = await discService.testConnection();\n      console.log('Supabase connection test:', connected);\n      if (!connected) {\n        setSubmitMessage('Demo Mode: Form data logged to console (Supabase not configured)');\n        console.log('Form data:', formData, 'Images:', selectedImages.length);\n        setTimeout(() => {\n          onNavigate('home');\n        }, 2000);\n        return;\n      }\n      let imageUrls = [];\n\n      // Upload images if any are selected\n      if (selectedImages.length > 0) {\n        console.log('Starting image upload for user:', user.id);\n        setSubmitMessage('Uploading images...');\n        const {\n          urls,\n          error: imageError\n        } = await imageService.uploadImages(selectedImages, user.id);\n        console.log('Image upload result:', {\n          urls,\n          error: imageError\n        });\n        if (imageError) {\n          console.error('Image upload error:', imageError);\n          setSubmitMessage(`Error uploading images: ${imageError.message || JSON.stringify(imageError)}`);\n          return;\n        }\n        imageUrls = urls;\n        console.log('Images uploaded successfully:', imageUrls);\n      }\n\n      // Prepare data for Supabase\n      const discData = {\n        finder_id: user.id,\n        // Use authenticated user's ID\n        brand: formData.brand,\n        mold: formData.mold || undefined,\n        disc_type: formData.discType || undefined,\n        color: formData.color,\n        weight: formData.weight ? parseInt(formData.weight) : undefined,\n        condition: formData.condition || undefined,\n        plastic_type: formData.plasticType || undefined,\n        stamp_text: formData.stampText || undefined,\n        phone_number: formData.phoneNumber || undefined,\n        name_on_disc: formData.nameOnDisc || undefined,\n        location_found: formData.locationFound,\n        found_date: formData.foundDate,\n        description: formData.description || undefined,\n        image_urls: imageUrls.length > 0 ? imageUrls : undefined\n      };\n      setSubmitMessage('Saving disc information...');\n      const {\n        data,\n        error\n      } = await discService.createFoundDisc(discData);\n      if (error) {\n        // If disc creation failed but images were uploaded, clean up the images\n        if (imageUrls.length > 0) {\n          await imageService.deleteImages(imageUrls);\n        }\n        setSubmitMessage(`Error: ${(error === null || error === void 0 ? void 0 : error.message) || 'Unknown error occurred'}`);\n      } else {\n        setSubmitMessage('Found disc reported successfully!');\n        console.log('Saved disc:', data);\n        setTimeout(() => {\n          onNavigate('home');\n        }, 2000);\n      }\n    } catch (error) {\n      setSubmitMessage('Demo Mode: Form data logged to console (Supabase not configured)');\n      console.log('Form data:', formData, 'Images:', selectedImages.length);\n      setTimeout(() => {\n        onNavigate('home');\n      }, 2000);\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"form-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"form-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"back-button\",\n        onClick: () => onNavigate('home'),\n        children: \"\\u2190 Back to Home\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 513,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n        children: \"Report Found Disc\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 516,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Help reunite a disc with its owner by providing details about the disc you found.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 517,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 512,\n      columnNumber: 7\n    }, this), !user && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"auth-notice\",\n      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n          children: \"Sign in required:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 523,\n          columnNumber: 13\n        }, this), \" You need to be signed in to report found discs and upload images.\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 522,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"button primary\",\n        onClick: () => onNavigate('login'),\n        children: \"Sign In\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 525,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 521,\n      columnNumber: 9\n    }, this), submitMessage && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `status-message ${submitMessage.includes('Error') ? 'error' : 'success'}`,\n      children: submitMessage\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 535,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n      className: \"disc-form\",\n      onSubmit: handleSubmit,\n      style: {\n        opacity: !user ? 0.6 : 1\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Disc Information\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 542,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-row\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"brand\",\n              children: \"Brand *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 545,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              id: \"brand\",\n              name: \"brand\",\n              value: formData.brand,\n              onChange: handleInputChange,\n              required: true,\n              placeholder: \"e.g., Innova, Discraft, Dynamic Discs\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 546,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 544,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"mold\",\n              children: \"Mold *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 557,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              id: \"mold\",\n              name: \"mold\",\n              value: formData.mold,\n              onChange: handleInputChange,\n              required: true,\n              placeholder: \"e.g., Destroyer, Buzzz, Judge\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 558,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 556,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 543,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-row\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"discType\",\n              children: \"Disc Type\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 572,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              id: \"discType\",\n              name: \"discType\",\n              value: formData.discType,\n              onChange: handleInputChange,\n              placeholder: \"e.g., Putter, Midrange, Fairway Driver, Distance Driver\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 573,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 571,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"color\",\n              children: \"Color *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 583,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              id: \"color\",\n              name: \"color\",\n              value: formData.color,\n              onChange: handleInputChange,\n              required: true,\n              placeholder: \"e.g., Blue, Red, Orange\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 584,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 582,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 570,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-row\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"weight\",\n              children: \"Weight (grams)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 598,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"number\",\n              id: \"weight\",\n              name: \"weight\",\n              value: formData.weight,\n              onChange: handleInputChange,\n              placeholder: \"e.g., 175\",\n              min: \"100\",\n              max: \"200\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 599,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 597,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"condition\",\n              children: \"Condition\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 611,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              id: \"condition\",\n              name: \"condition\",\n              value: formData.condition,\n              onChange: handleInputChange,\n              placeholder: \"e.g., New, Excellent, Good, Fair, Poor\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 612,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 610,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 596,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 541,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Additional Details\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 625,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-row\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"plasticType\",\n              children: \"Plastic Type\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 628,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              id: \"plasticType\",\n              name: \"plasticType\",\n              value: formData.plasticType,\n              onChange: handleInputChange,\n              placeholder: \"e.g., Champion, ESP, Lucid\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 629,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 627,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"stampText\",\n              children: \"Stamp/Text\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 639,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              id: \"stampText\",\n              name: \"stampText\",\n              value: formData.stampText,\n              onChange: handleInputChange,\n              placeholder: \"Any text or stamps on the disc\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 640,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 638,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 626,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-row\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"phoneNumber\",\n              children: \"Phone Number on Disc\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 653,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"tel\",\n              id: \"phoneNumber\",\n              name: \"phoneNumber\",\n              value: formData.phoneNumber,\n              onChange: handleInputChange,\n              placeholder: \"Phone number written on disc\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 654,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 652,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"nameOnDisc\",\n              children: \"Name on Disc\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 664,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              id: \"nameOnDisc\",\n              name: \"nameOnDisc\",\n              value: formData.nameOnDisc,\n              onChange: handleInputChange,\n              placeholder: \"Name written on disc\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 665,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 663,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 651,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 624,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Location & Date\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 678,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-row\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"locationFound\",\n              children: \"Location Found *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 681,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              id: \"locationFound\",\n              name: \"locationFound\",\n              value: formData.locationFound,\n              onChange: handleInputChange,\n              required: true,\n              placeholder: \"e.g., Maple Hill Disc Golf Course, Hole 7\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 682,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 680,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"foundDate\",\n              children: \"Date Found *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 693,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"date\",\n              id: \"foundDate\",\n              name: \"foundDate\",\n              value: formData.foundDate,\n              onChange: handleInputChange,\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 694,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 692,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 679,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"description\",\n            children: \"Additional Description\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 706,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n            id: \"description\",\n            name: \"description\",\n            value: formData.description,\n            onChange: handleInputChange,\n            rows: 4,\n            placeholder: \"Any additional details about where or how you found the disc...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 707,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 705,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 677,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Disc Images\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 719,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"form-section-description\",\n          children: \"Adding photos helps disc owners identify their disc more easily. You can upload up to 2 images.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 720,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(ImageUpload, {\n          onImagesChange: setSelectedImages,\n          maxImages: 2,\n          maxSizePerImage: 10,\n          disabled: isSubmitting\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 723,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 718,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-actions\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"button\",\n          className: \"button secondary\",\n          onClick: () => onNavigate('home'),\n          disabled: isSubmitting,\n          children: \"Cancel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 732,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"submit\",\n          className: \"button primary\",\n          disabled: isSubmitting || !user,\n          children: isSubmitting ? 'Submitting...' : !user ? 'Sign In Required' : 'Report Found Disc'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 740,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 731,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 540,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 511,\n    columnNumber: 5\n  }, this);\n}\n_s3(ReportFound, \"raAaofL9jFQzRVh1hGbrBdCaX20=\", false, function () {\n  return [useAuth];\n});\n_c5 = ReportFound;\nfunction SearchLost({\n  onNavigate\n}) {\n  _s4();\n  const {\n    user,\n    isGuest,\n    userRole\n  } = useAuth();\n  const [searchCriteria, setSearchCriteria] = useState({\n    brand: '',\n    mold: '',\n    color: '',\n    discType: '',\n    locationFound: ''\n  });\n  const [foundDiscs, setFoundDiscs] = useState([]);\n  const [isSearching, setIsSearching] = useState(false);\n  const [hasSearched, setHasSearched] = useState(false);\n  const handleReturnStatusUpdate = (discId, newStatus) => {\n    // Update the disc in the local state\n    setFoundDiscs(prev => prev.map(disc => disc.id === discId ? {\n      ...disc,\n      return_status: newStatus,\n      returned_at: new Date().toISOString()\n    } : disc));\n  };\n  const handleInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setSearchCriteria(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n  const handleSearch = async e => {\n    e.preventDefault();\n    setIsSearching(true);\n    setHasSearched(true);\n    try {\n      const {\n        data,\n        error\n      } = await discService.searchFoundDiscs(searchCriteria);\n      if (error) {\n        console.error('Search error:', error);\n        setFoundDiscs([]);\n      } else {\n        console.log('Search results:', data);\n        setFoundDiscs(data || []);\n      }\n    } catch (error) {\n      console.error('Search failed:', error);\n      setFoundDiscs([]);\n    } finally {\n      setIsSearching(false);\n    }\n  };\n  const loadAllDiscs = async () => {\n    setIsSearching(true);\n    setHasSearched(true);\n    try {\n      const {\n        data,\n        error\n      } = await discService.getFoundDiscs();\n      if (error) {\n        console.error('Load error:', error);\n        setFoundDiscs([]);\n      } else {\n        console.log('Load all results:', data);\n        setFoundDiscs(data || []);\n      }\n    } catch (error) {\n      console.error('Load failed:', error);\n      setFoundDiscs([]);\n    } finally {\n      setIsSearching(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"page-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"page-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"back-button\",\n        onClick: () => onNavigate('home'),\n        children: \"\\u2190 Back to Home\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 831,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n        children: \"Search Lost Discs\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 834,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Search through reported found discs to see if someone has found your lost disc.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 835,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 830,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"search-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"form\", {\n        className: \"search-form\",\n        onSubmit: handleSearch,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"search-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Search Criteria\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 841,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-row\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"search-brand\",\n                children: \"Brand\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 844,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                id: \"search-brand\",\n                name: \"brand\",\n                value: searchCriteria.brand,\n                onChange: handleInputChange,\n                placeholder: \"e.g., Innova, Discraft\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 845,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 843,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"search-mold\",\n                children: \"Mold\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 855,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                id: \"search-mold\",\n                name: \"mold\",\n                value: searchCriteria.mold,\n                onChange: handleInputChange,\n                placeholder: \"e.g., Destroyer, Buzzz\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 856,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 854,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 842,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-row\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"search-color\",\n                children: \"Color\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 869,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                id: \"search-color\",\n                name: \"color\",\n                value: searchCriteria.color,\n                onChange: handleInputChange,\n                placeholder: \"e.g., Blue, Red\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 870,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 868,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"search-discType\",\n                children: \"Disc Type\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 880,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                id: \"search-discType\",\n                name: \"discType\",\n                value: searchCriteria.discType,\n                onChange: handleInputChange,\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"Any type\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 887,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"putter\",\n                  children: \"Putter\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 888,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"midrange\",\n                  children: \"Midrange\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 889,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"fairway_driver\",\n                  children: \"Fairway Driver\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 890,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"distance_driver\",\n                  children: \"Distance Driver\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 891,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"approach\",\n                  children: \"Approach\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 892,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 881,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 879,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 867,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"search-location\",\n              children: \"Location\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 898,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              id: \"search-location\",\n              name: \"locationFound\",\n              value: searchCriteria.locationFound,\n              onChange: handleInputChange,\n              placeholder: \"e.g., Maple Hill, DeLaveaga\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 899,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 897,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 840,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"search-actions\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            className: \"button secondary\",\n            onClick: loadAllDiscs,\n            disabled: isSearching,\n            children: isSearching ? 'Loading...' : 'Show All Found Discs'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 911,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            className: \"button primary\",\n            disabled: isSearching,\n            children: isSearching ? 'Searching...' : 'Search'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 919,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 910,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 839,\n        columnNumber: 9\n      }, this), hasSearched && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"search-results\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: foundDiscs.length > 0 ? `Found ${foundDiscs.length} disc${foundDiscs.length === 1 ? '' : 's'}` : 'No discs found matching your criteria'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 931,\n          columnNumber: 13\n        }, this), foundDiscs.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"disc-grid\",\n          children: foundDiscs.map(disc => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"disc-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"disc-header\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                children: [disc.brand, \" \", disc.mold || 'Unknown Mold']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 943,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"disc-type\",\n                children: disc.disc_type || 'Unknown Type'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 944,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 942,\n              columnNumber: 21\n            }, this), (userRole === 'admin' || disc.return_status && disc.return_status !== 'Found') && /*#__PURE__*/_jsxDEV(ReturnStatusManager, {\n              discId: disc.id,\n              currentStatus: disc.return_status || 'Found',\n              onStatusUpdated: newStatus => handleReturnStatusUpdate(disc.id, newStatus),\n              disabled: userRole !== 'admin'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 949,\n              columnNumber: 23\n            }, this), disc.image_urls && disc.image_urls.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"disc-images\",\n              children: disc.image_urls.slice(0, 2).map((imageUrl, index) => /*#__PURE__*/_jsxDEV(\"img\", {\n                src: imageUrl,\n                alt: `${disc.brand} ${disc.mold || 'disc'} ${index + 1}`,\n                className: \"disc-image\",\n                onError: e => {\n                  // Hide broken images\n                  e.target.style.display = 'none';\n                }\n              }, index, false, {\n                fileName: _jsxFileName,\n                lineNumber: 960,\n                columnNumber: 27\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 958,\n              columnNumber: 23\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"disc-details\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"detail-row\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"label\",\n                  children: \"Color:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 976,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"value\",\n                  children: disc.color\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 977,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 975,\n                columnNumber: 23\n              }, this), disc.weight && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"detail-row\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"label\",\n                  children: \"Weight:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 982,\n                  columnNumber: 27\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"value\",\n                  children: [disc.weight, \"g\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 983,\n                  columnNumber: 27\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 981,\n                columnNumber: 25\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"detail-row\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"label\",\n                  children: \"Condition:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 988,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"value\",\n                  children: disc.condition || 'Unknown'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 989,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 987,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"detail-row\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"label\",\n                  children: \"Found at:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 993,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"value\",\n                  children: disc.location_found\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 994,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 992,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"detail-row\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"label\",\n                  children: \"Found on:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 998,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"value\",\n                  children: new Date(disc.found_date).toLocaleDateString()\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 999,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 997,\n                columnNumber: 23\n              }, this), disc.phone_number && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"detail-row\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"label\",\n                  children: \"Phone on disc:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1004,\n                  columnNumber: 27\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"value\",\n                  children: disc.phone_number\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1005,\n                  columnNumber: 27\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1003,\n                columnNumber: 25\n              }, this), disc.name_on_disc && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"detail-row\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"label\",\n                  children: \"Name on disc:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1011,\n                  columnNumber: 27\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"value\",\n                  children: disc.name_on_disc\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1012,\n                  columnNumber: 27\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1010,\n                columnNumber: 25\n              }, this), disc.description && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"detail-row\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"label\",\n                  children: \"Description:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1018,\n                  columnNumber: 27\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"value\",\n                  children: disc.description\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1019,\n                  columnNumber: 27\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1017,\n                columnNumber: 25\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 974,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"disc-actions\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"button primary small\",\n                children: \"Contact Finder\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1025,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"button secondary small\",\n                children: \"Report as Mine\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1028,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1024,\n              columnNumber: 21\n            }, this)]\n          }, disc.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 941,\n            columnNumber: 19\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 939,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 930,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 838,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 829,\n    columnNumber: 5\n  }, this);\n}\n_s4(SearchLost, \"Kr/yxvuhUZD0DXNosjxS/ZclWKs=\", false, function () {\n  return [useAuth];\n});\n_c6 = SearchLost;\nfunction Login({\n  onNavigate\n}) {\n  _s5();\n  const {\n    signIn,\n    signUp\n  } = useAuth();\n  const [isLogin, setIsLogin] = useState(true);\n  const [formData, setFormData] = useState({\n    email: '',\n    password: '',\n    fullName: '',\n    confirmPassword: ''\n  });\n  const [loading, setLoading] = useState(false);\n  const [message, setMessage] = useState('');\n  const handleInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setLoading(true);\n    setMessage('');\n    try {\n      if (isLogin) {\n        const {\n          error\n        } = await signIn(formData.email, formData.password);\n        if (error) {\n          setMessage(error.message);\n        } else {\n          setMessage('Signed in successfully!');\n          setTimeout(() => onNavigate('home'), 1000);\n        }\n      } else {\n        if (formData.password !== formData.confirmPassword) {\n          setMessage('Passwords do not match');\n          setLoading(false);\n          return;\n        }\n        if (formData.password.length < 6) {\n          setMessage('Password must be at least 6 characters');\n          setLoading(false);\n          return;\n        }\n        const {\n          error\n        } = await signUp(formData.email, formData.password, formData.fullName);\n        if (error) {\n          setMessage(error.message);\n        } else {\n          setMessage('Account created! Please check your email to verify your account.');\n        }\n      }\n    } catch (error) {\n      setMessage('An unexpected error occurred');\n    } finally {\n      setLoading(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"page-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"page-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"back-button\",\n        onClick: () => onNavigate('home'),\n        children: \"\\u2190 Back to Home\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1106,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n        children: isLogin ? 'Sign In' : 'Create Account'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1109,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: isLogin ? 'Sign in to your account' : 'Create an account to report and search for discs'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1110,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1105,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"auth-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"auth-tabs\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: `auth-tab ${isLogin ? 'active' : ''}`,\n          onClick: () => setIsLogin(true),\n          children: \"Sign In\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1115,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: `auth-tab ${!isLogin ? 'active' : ''}`,\n          onClick: () => setIsLogin(false),\n          children: \"Sign Up\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1121,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1114,\n        columnNumber: 9\n      }, this), message && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `status-message ${message.includes('error') || message.includes('Error') ? 'error' : 'success'}`,\n        children: message\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1130,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n        className: \"auth-form\",\n        onSubmit: handleSubmit,\n        children: [!isLogin && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"fullName\",\n            children: \"Full Name\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1138,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            id: \"fullName\",\n            name: \"fullName\",\n            value: formData.fullName,\n            onChange: handleInputChange,\n            required: !isLogin,\n            placeholder: \"Your full name\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1139,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1137,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"email\",\n            children: \"Email\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1152,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"email\",\n            id: \"email\",\n            name: \"email\",\n            value: formData.email,\n            onChange: handleInputChange,\n            required: true,\n            placeholder: \"<EMAIL>\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1153,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1151,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"password\",\n            children: \"Password\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1165,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"password\",\n            id: \"password\",\n            name: \"password\",\n            value: formData.password,\n            onChange: handleInputChange,\n            required: true,\n            placeholder: isLogin ? \"Your password\" : \"At least 6 characters\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1166,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1164,\n          columnNumber: 11\n        }, this), !isLogin && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"confirmPassword\",\n            children: \"Confirm Password\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1179,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"password\",\n            id: \"confirmPassword\",\n            name: \"confirmPassword\",\n            value: formData.confirmPassword,\n            onChange: handleInputChange,\n            required: !isLogin,\n            placeholder: \"Confirm your password\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1180,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1178,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"submit\",\n          className: \"button primary full-width\",\n          disabled: loading,\n          children: loading ? 'Please wait...' : isLogin ? 'Sign In' : 'Create Account'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1192,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1135,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1113,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 1104,\n    columnNumber: 5\n  }, this);\n}\n_s5(Login, \"DtWoNHOVg6rzGvvHVdAJmIhKXiE=\", false, function () {\n  return [useAuth];\n});\n_c7 = Login;\nexport default App;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7;\n$RefreshReg$(_c, \"AppContent\");\n$RefreshReg$(_c2, \"App\");\n$RefreshReg$(_c3, \"AdminDashboard\");\n$RefreshReg$(_c4, \"Home\");\n$RefreshReg$(_c5, \"ReportFound\");\n$RefreshReg$(_c6, \"SearchLost\");\n$RefreshReg$(_c7, \"Login\");", "map": {"version": 3, "names": ["React", "useState", "discService", "imageService", "<PERSON>th<PERSON><PERSON><PERSON>", "useAuth", "ImageUpload", "ReturnStatusManager", "jsxDEV", "_jsxDEV", "A<PERSON><PERSON><PERSON>nt", "_s", "currentPage", "setCurrentPage", "user", "userRole", "signOut", "loading", "renderPage", "Home", "onNavigate", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "ReportFound", "SearchLost", "<PERSON><PERSON>", "AdminDashboard", "handleSignOut", "className", "children", "style", "fontSize", "color", "marginTop", "onClick", "cursor", "email", "_c", "App", "_c2", "_s2", "allDiscs", "setAllDiscs", "isLoading", "setIsLoading", "filter", "setFilter", "loadAllDiscs", "data", "error", "getAdminFoundDiscs", "console", "log", "useEffect", "handleReturnStatusUpdate", "discId", "newStatus", "prev", "map", "disc", "id", "return_status", "returned_at", "Date", "toISOString", "filteredDiscs", "statusCounts", "reduce", "counts", "status", "Object", "entries", "count", "htmlFor", "value", "onChange", "e", "target", "length", "brand", "mold", "disc_type", "currentStatus", "onStatusUpdated", "disabled", "image_urls", "slice", "imageUrl", "index", "src", "alt", "onError", "display", "location_found", "found_date", "toLocaleDateString", "returned_notes", "_c3", "_c4", "_s3", "isGuest", "formData", "setFormData", "discType", "weight", "condition", "plasticType", "stampText", "phoneNumber", "nameOnDisc", "locationFound", "foundDate", "description", "selectedImages", "setSelectedImages", "isSubmitting", "setIsSubmitting", "submitMessage", "setSubmitMessage", "handleInputChange", "name", "handleSubmit", "preventDefault", "connected", "testConnection", "setTimeout", "imageUrls", "urls", "imageError", "uploadImages", "message", "JSON", "stringify", "discData", "finder_id", "undefined", "parseInt", "plastic_type", "stamp_text", "phone_number", "name_on_disc", "createFoundDisc", "deleteImages", "includes", "onSubmit", "opacity", "type", "required", "placeholder", "min", "max", "rows", "onImagesChange", "maxImages", "maxSizePerImage", "_c5", "_s4", "searchCriteria", "setSearchCriteria", "foundDiscs", "setFoundDiscs", "isSearching", "setIsSearching", "hasSearched", "setHasSearched", "handleSearch", "searchFoundDiscs", "getFoundDiscs", "_c6", "_s5", "signIn", "signUp", "is<PERSON>ogin", "setIsLogin", "password", "fullName", "confirmPassword", "setLoading", "setMessage", "_c7", "$RefreshReg$"], "sources": ["C:/Users/<USER>/lostandfound/discfinder-app/src/App.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { discService, imageService, ReturnStatus } from './lib/supabase';\nimport { AuthProvider, useAuth } from './contexts/AuthContext';\nimport { ImageUpload } from './components/ImageUpload';\nimport { ReturnStatusManager } from './components/ReturnStatusManager';\n\ntype Page = 'home' | 'report-found' | 'search-lost' | 'login' | 'admin';\n\nfunction AppContent() {\n  const [currentPage, setCurrentPage] = useState<Page>('home');\n  const { user, userRole, signOut, loading } = useAuth();\n\n  const renderPage = () => {\n    switch (currentPage) {\n      case 'home':\n        return <Home onNavigate={setCurrentPage} />;\n      case 'report-found':\n        return <ReportFound onNavigate={setCurrentPage} />;\n      case 'search-lost':\n        return <SearchLost onNavigate={setCurrentPage} />;\n      case 'login':\n        return <Login onNavigate={setCurrentPage} />;\n      case 'admin':\n        return <AdminDashboard onNavigate={setCurrentPage} />;\n      default:\n        return <Home onNavigate={setCurrentPage} />;\n    }\n  };\n\n  const handleSignOut = async () => {\n    await signOut();\n    setCurrentPage('home');\n  };\n\n  if (loading) {\n    return (\n      <div className=\"app\">\n        <div className=\"loading-container\">\n          <div className=\"loading-spinner\"></div>\n          <p>Loading...</p>\n          <p style={{ fontSize: '0.8rem', color: '#666', marginTop: '1rem' }}>\n            If this takes too long, check the browser console for errors\n          </p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"app\">\n      <nav className=\"navbar\">\n        <div className=\"nav-container\">\n          <div className=\"logo\" onClick={() => setCurrentPage('home')} style={{ cursor: 'pointer' }}>\n            DiscFinder\n          </div>\n          <div className=\"nav-buttons\">\n            <button className=\"nav-button\" onClick={() => setCurrentPage('report-found')}>\n              Report Found\n            </button>\n            <button className=\"nav-button\" onClick={() => setCurrentPage('search-lost')}>\n              Search Lost\n            </button>\n\n            {user ? (\n              <div className=\"user-menu\">\n                <span className=\"user-info\">\n                  {user.email} ({userRole})\n                </span>\n                {userRole === 'admin' && (\n                  <button className=\"nav-button\" onClick={() => setCurrentPage('admin')}>\n                    Admin\n                  </button>\n                )}\n                <button className=\"nav-button\" onClick={handleSignOut}>\n                  Sign Out\n                </button>\n              </div>\n            ) : (\n              <button className=\"nav-button primary\" onClick={() => setCurrentPage('login')}>\n                Sign In\n              </button>\n            )}\n          </div>\n        </div>\n      </nav>\n\n      <main className=\"main-container\">\n        {renderPage()}\n      </main>\n    </div>\n  );\n}\n\nfunction App() {\n  return (\n    <AuthProvider>\n      <AppContent />\n    </AuthProvider>\n  );\n}\n\ninterface PageProps {\n  onNavigate: (page: Page) => void;\n}\n\nfunction AdminDashboard({ onNavigate }: PageProps) {\n  const { userRole } = useAuth();\n  const [allDiscs, setAllDiscs] = useState<any[]>([]);\n  const [isLoading, setIsLoading] = useState(false);\n  const [filter, setFilter] = useState<ReturnStatus | 'All'>('All');\n\n  const loadAllDiscs = async () => {\n    setIsLoading(true);\n    try {\n      const { data, error } = await discService.getAdminFoundDiscs();\n      if (error) {\n        console.error('Error loading admin discs:', error);\n      } else {\n        console.log('Admin discs loaded:', data);\n        setAllDiscs(data || []);\n      }\n    } catch (error) {\n      console.error('Error loading admin discs:', error);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  React.useEffect(() => {\n    loadAllDiscs();\n  }, []);\n\n  const handleReturnStatusUpdate = (discId: string, newStatus: ReturnStatus) => {\n    setAllDiscs(prev => prev.map(disc =>\n      disc.id === discId\n        ? { ...disc, return_status: newStatus, returned_at: new Date().toISOString() }\n        : disc\n    ));\n  };\n\n  const filteredDiscs = filter === 'All'\n    ? allDiscs\n    : allDiscs.filter(disc => disc.return_status === filter);\n\n  const statusCounts = allDiscs.reduce((counts, disc) => {\n    const status = disc.return_status || 'Found';\n    counts[status] = (counts[status] || 0) + 1;\n    return counts;\n  }, {} as Record<string, number>);\n\n  // Redirect if not admin\n  if (userRole !== 'admin') {\n    return (\n      <div className=\"form-container\">\n        <div className=\"form-header\">\n          <button className=\"back-button\" onClick={() => onNavigate('home')}>\n            ← Back to Home\n          </button>\n          <h1>Access Denied</h1>\n          <p>You need admin privileges to access this page.</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"form-container\">\n      <div className=\"form-header\">\n        <button className=\"back-button\" onClick={() => onNavigate('home')}>\n          ← Back to Home\n        </button>\n        <h1>Admin Dashboard</h1>\n        <p>Manage all found discs and their return status.</p>\n      </div>\n\n      {/* Status Summary */}\n      <div className=\"status-summary\">\n        <h3>Status Summary</h3>\n        <div className=\"status-counts\">\n          {Object.entries(statusCounts).map(([status, count]) => (\n            <div key={status} className=\"status-count\">\n              <span className=\"count\">{count as number}</span>\n              <span className=\"status\">{status}</span>\n            </div>\n          ))}\n        </div>\n      </div>\n\n      {/* Filter Controls */}\n      <div className=\"filter-controls\">\n        <label htmlFor=\"status-filter\">Filter by Status:</label>\n        <select\n          id=\"status-filter\"\n          value={filter}\n          onChange={(e) => setFilter(e.target.value as ReturnStatus | 'All')}\n        >\n          <option value=\"All\">All Statuses</option>\n          <option value=\"Found\">Found</option>\n          <option value=\"Returned to Owner\">Returned to Owner</option>\n          <option value=\"Donated\">Donated</option>\n          <option value=\"Sold\">Sold</option>\n          <option value=\"Trashed\">Trashed</option>\n        </select>\n      </div>\n\n      {/* Disc List */}\n      {isLoading ? (\n        <div className=\"loading-container\">\n          <div className=\"loading-spinner\"></div>\n          <p>Loading discs...</p>\n        </div>\n      ) : (\n        <div className=\"admin-disc-grid\">\n          {filteredDiscs.length === 0 ? (\n            <p>No discs found for the selected filter.</p>\n          ) : (\n            filteredDiscs.map((disc) => (\n              <div key={disc.id} className=\"admin-disc-card\">\n                <div className=\"disc-header\">\n                  <h4>{disc.brand} {disc.mold || 'Unknown Mold'}</h4>\n                  <span className=\"disc-type\">{disc.disc_type || 'Unknown Type'}</span>\n                </div>\n\n                <ReturnStatusManager\n                  discId={disc.id}\n                  currentStatus={disc.return_status || 'Found'}\n                  onStatusUpdated={(newStatus) => handleReturnStatusUpdate(disc.id, newStatus)}\n                  disabled={false}\n                />\n\n                {disc.image_urls && disc.image_urls.length > 0 && (\n                  <div className=\"disc-images\">\n                    {disc.image_urls.slice(0, 2).map((imageUrl: string, index: number) => (\n                      <img\n                        key={index}\n                        src={imageUrl}\n                        alt={`${disc.brand} ${disc.mold || 'disc'} ${index + 1}`}\n                        className=\"disc-image\"\n                        onError={(e) => {\n                          (e.target as HTMLImageElement).style.display = 'none';\n                        }}\n                      />\n                    ))}\n                  </div>\n                )}\n\n                <div className=\"disc-details\">\n                  <div className=\"detail-row\">\n                    <span className=\"label\">Color:</span>\n                    <span className=\"value\">{disc.color}</span>\n                  </div>\n                  <div className=\"detail-row\">\n                    <span className=\"label\">Location Found:</span>\n                    <span className=\"value\">{disc.location_found}</span>\n                  </div>\n                  <div className=\"detail-row\">\n                    <span className=\"label\">Found Date:</span>\n                    <span className=\"value\">{new Date(disc.found_date).toLocaleDateString()}</span>\n                  </div>\n                  {disc.returned_at && (\n                    <div className=\"detail-row\">\n                      <span className=\"label\">Returned Date:</span>\n                      <span className=\"value\">{new Date(disc.returned_at).toLocaleDateString()}</span>\n                    </div>\n                  )}\n                  {disc.returned_notes && (\n                    <div className=\"detail-row\">\n                      <span className=\"label\">Notes:</span>\n                      <span className=\"value\">{disc.returned_notes}</span>\n                    </div>\n                  )}\n                </div>\n              </div>\n            ))\n          )}\n        </div>\n      )}\n    </div>\n  );\n}\n\nfunction Home({ onNavigate }: PageProps) {\n  return (\n    <div>\n      <div className=\"hero\">\n        <h1>Lost Your Disc?</h1>\n        <p>\n          DiscFinder helps disc golf players reunite with their lost discs.\n          Report found discs or search for your lost ones in our community database.\n        </p>\n\n        <div className=\"hero-buttons\">\n          <button className=\"hero-button primary\" onClick={() => onNavigate('report-found')}>\n            Report Found Disc\n          </button>\n          <button className=\"hero-button secondary\" onClick={() => onNavigate('search-lost')}>\n            Search Lost Discs\n          </button>\n        </div>\n      </div>\n\n      <div className=\"features\">\n        <div className=\"feature-card\">\n          <div className=\"feature-icon\">\n            <div>🔍</div>\n          </div>\n          <h3>Smart Matching</h3>\n          <p>\n            Our intelligent system matches found and lost discs based on brand, model, color, and location.\n          </p>\n        </div>\n\n        <div className=\"feature-card\">\n          <div className=\"feature-icon\">\n            <div>📍</div>\n          </div>\n          <h3>Location Based</h3>\n          <p>\n            Find discs near where you lost them with our location-based search and matching.\n          </p>\n        </div>\n\n        <div className=\"feature-card\">\n          <div className=\"feature-icon\">\n            <div>💬</div>\n          </div>\n          <h3>Easy Communication</h3>\n          <p>\n            Connect directly with finders and owners through our secure messaging system.\n          </p>\n        </div>\n      </div>\n\n      <div className=\"stats\">\n        <div className=\"stats-grid\">\n          <div className=\"stat-item\">\n            <div className=\"stat-number\">500+</div>\n            <div className=\"stat-label\">Discs Reunited</div>\n          </div>\n          <div className=\"stat-item\">\n            <div className=\"stat-number\">1,200+</div>\n            <div className=\"stat-label\">Active Users</div>\n          </div>\n          <div className=\"stat-item\">\n            <div className=\"stat-number\">95%</div>\n            <div className=\"stat-label\">Success Rate</div>\n          </div>\n        </div>\n      </div>\n\n      <div className=\"cta\">\n        <h2>Join the Community</h2>\n        <p>\n          Create an account to report found discs, search for lost ones, and help fellow disc golfers.\n        </p>\n        <button className=\"cta-button\" onClick={() => onNavigate('login')}>\n          Sign Up Now\n        </button>\n      </div>\n    </div>\n  );\n}\n\nfunction ReportFound({ onNavigate }: PageProps) {\n  const { user, isGuest, userRole } = useAuth();\n  const [formData, setFormData] = useState({\n    brand: '',\n    mold: '',\n    discType: '',\n    color: '',\n    weight: '',\n    condition: '',\n    plasticType: '',\n    stampText: '',\n    phoneNumber: '',\n    nameOnDisc: '',\n    locationFound: '',\n    foundDate: '',\n    description: '',\n  });\n  const [selectedImages, setSelectedImages] = useState<File[]>([]);\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [submitMessage, setSubmitMessage] = useState('');\n\n  // Require authentication to report found discs\n  if (isGuest) {\n    return (\n      <div className=\"page-container\">\n        <div className=\"page-header\">\n          <button className=\"back-button\" onClick={() => onNavigate('home')}>\n            ← Back to Home\n          </button>\n          <h1>Report a Found Disc</h1>\n          <p>You must be signed in to report found discs.</p>\n        </div>\n        <div className=\"auth-required\">\n          <h2>Authentication Required</h2>\n          <p>Please sign in or create an account to report found discs. This helps us maintain data quality and allows disc owners to contact you.</p>\n          <button className=\"button primary\" onClick={() => onNavigate('login')}>\n            Sign In / Sign Up\n          </button>\n        </div>\n      </div>\n    );\n  }\n\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setIsSubmitting(true);\n    setSubmitMessage('');\n\n    try {\n      console.log('Form submission started');\n      console.log('User:', user);\n      console.log('Selected images:', selectedImages.length);\n\n      // Check if user is authenticated for image upload\n      if (selectedImages.length > 0 && !user) {\n        setSubmitMessage('Error: You must be signed in to upload images');\n        return;\n      }\n\n      // Test connection first\n      const { connected } = await discService.testConnection();\n      console.log('Supabase connection test:', connected);\n\n      if (!connected) {\n        setSubmitMessage('Demo Mode: Form data logged to console (Supabase not configured)');\n        console.log('Form data:', formData, 'Images:', selectedImages.length);\n        setTimeout(() => {\n          onNavigate('home');\n        }, 2000);\n        return;\n      }\n\n      let imageUrls: string[] = [];\n\n      // Upload images if any are selected\n      if (selectedImages.length > 0) {\n        console.log('Starting image upload for user:', user!.id);\n        setSubmitMessage('Uploading images...');\n        const { urls, error: imageError } = await imageService.uploadImages(selectedImages, user!.id);\n\n        console.log('Image upload result:', { urls, error: imageError });\n\n        if (imageError) {\n          console.error('Image upload error:', imageError);\n          setSubmitMessage(`Error uploading images: ${imageError.message || JSON.stringify(imageError)}`);\n          return;\n        }\n\n        imageUrls = urls;\n        console.log('Images uploaded successfully:', imageUrls);\n      }\n\n      // Prepare data for Supabase\n      const discData = {\n        finder_id: user!.id, // Use authenticated user's ID\n        brand: formData.brand,\n        mold: formData.mold || undefined,\n        disc_type: formData.discType || undefined,\n        color: formData.color,\n        weight: formData.weight ? parseInt(formData.weight) : undefined,\n        condition: formData.condition || undefined,\n        plastic_type: formData.plasticType || undefined,\n        stamp_text: formData.stampText || undefined,\n        phone_number: formData.phoneNumber || undefined,\n        name_on_disc: formData.nameOnDisc || undefined,\n        location_found: formData.locationFound,\n        found_date: formData.foundDate,\n        description: formData.description || undefined,\n        image_urls: imageUrls.length > 0 ? imageUrls : undefined,\n      };\n\n      setSubmitMessage('Saving disc information...');\n      const { data, error } = await discService.createFoundDisc(discData);\n\n      if (error) {\n        // If disc creation failed but images were uploaded, clean up the images\n        if (imageUrls.length > 0) {\n          await imageService.deleteImages(imageUrls);\n        }\n        setSubmitMessage(`Error: ${(error as any)?.message || 'Unknown error occurred'}`);\n      } else {\n        setSubmitMessage('Found disc reported successfully!');\n        console.log('Saved disc:', data);\n        setTimeout(() => {\n          onNavigate('home');\n        }, 2000);\n      }\n    } catch (error) {\n      setSubmitMessage('Demo Mode: Form data logged to console (Supabase not configured)');\n      console.log('Form data:', formData, 'Images:', selectedImages.length);\n      setTimeout(() => {\n        onNavigate('home');\n      }, 2000);\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  return (\n    <div className=\"form-container\">\n      <div className=\"form-header\">\n        <button className=\"back-button\" onClick={() => onNavigate('home')}>\n          ← Back to Home\n        </button>\n        <h1>Report Found Disc</h1>\n        <p>Help reunite a disc with its owner by providing details about the disc you found.</p>\n      </div>\n\n      {!user && (\n        <div className=\"auth-notice\">\n          <p>\n            <strong>Sign in required:</strong> You need to be signed in to report found discs and upload images.\n          </p>\n          <button\n            className=\"button primary\"\n            onClick={() => onNavigate('login')}\n          >\n            Sign In\n          </button>\n        </div>\n      )}\n\n      {submitMessage && (\n        <div className={`status-message ${submitMessage.includes('Error') ? 'error' : 'success'}`}>\n          {submitMessage}\n        </div>\n      )}\n\n      <form className=\"disc-form\" onSubmit={handleSubmit} style={{ opacity: !user ? 0.6 : 1 }}>\n        <div className=\"form-section\">\n          <h3>Disc Information</h3>\n          <div className=\"form-row\">\n            <div className=\"form-group\">\n              <label htmlFor=\"brand\">Brand *</label>\n              <input\n                type=\"text\"\n                id=\"brand\"\n                name=\"brand\"\n                value={formData.brand}\n                onChange={handleInputChange}\n                required\n                placeholder=\"e.g., Innova, Discraft, Dynamic Discs\"\n              />\n            </div>\n            <div className=\"form-group\">\n              <label htmlFor=\"mold\">Mold *</label>\n              <input\n                type=\"text\"\n                id=\"mold\"\n                name=\"mold\"\n                value={formData.mold}\n                onChange={handleInputChange}\n                required\n                placeholder=\"e.g., Destroyer, Buzzz, Judge\"\n              />\n            </div>\n          </div>\n\n          <div className=\"form-row\">\n            <div className=\"form-group\">\n              <label htmlFor=\"discType\">Disc Type</label>\n              <input\n                type=\"text\"\n                id=\"discType\"\n                name=\"discType\"\n                value={formData.discType}\n                onChange={handleInputChange}\n                placeholder=\"e.g., Putter, Midrange, Fairway Driver, Distance Driver\"\n              />\n            </div>\n            <div className=\"form-group\">\n              <label htmlFor=\"color\">Color *</label>\n              <input\n                type=\"text\"\n                id=\"color\"\n                name=\"color\"\n                value={formData.color}\n                onChange={handleInputChange}\n                required\n                placeholder=\"e.g., Blue, Red, Orange\"\n              />\n            </div>\n          </div>\n\n          <div className=\"form-row\">\n            <div className=\"form-group\">\n              <label htmlFor=\"weight\">Weight (grams)</label>\n              <input\n                type=\"number\"\n                id=\"weight\"\n                name=\"weight\"\n                value={formData.weight}\n                onChange={handleInputChange}\n                placeholder=\"e.g., 175\"\n                min=\"100\"\n                max=\"200\"\n              />\n            </div>\n            <div className=\"form-group\">\n              <label htmlFor=\"condition\">Condition</label>\n              <input\n                type=\"text\"\n                id=\"condition\"\n                name=\"condition\"\n                value={formData.condition}\n                onChange={handleInputChange}\n                placeholder=\"e.g., New, Excellent, Good, Fair, Poor\"\n              />\n            </div>\n          </div>\n        </div>\n\n        <div className=\"form-section\">\n          <h3>Additional Details</h3>\n          <div className=\"form-row\">\n            <div className=\"form-group\">\n              <label htmlFor=\"plasticType\">Plastic Type</label>\n              <input\n                type=\"text\"\n                id=\"plasticType\"\n                name=\"plasticType\"\n                value={formData.plasticType}\n                onChange={handleInputChange}\n                placeholder=\"e.g., Champion, ESP, Lucid\"\n              />\n            </div>\n            <div className=\"form-group\">\n              <label htmlFor=\"stampText\">Stamp/Text</label>\n              <input\n                type=\"text\"\n                id=\"stampText\"\n                name=\"stampText\"\n                value={formData.stampText}\n                onChange={handleInputChange}\n                placeholder=\"Any text or stamps on the disc\"\n              />\n            </div>\n          </div>\n\n          <div className=\"form-row\">\n            <div className=\"form-group\">\n              <label htmlFor=\"phoneNumber\">Phone Number on Disc</label>\n              <input\n                type=\"tel\"\n                id=\"phoneNumber\"\n                name=\"phoneNumber\"\n                value={formData.phoneNumber}\n                onChange={handleInputChange}\n                placeholder=\"Phone number written on disc\"\n              />\n            </div>\n            <div className=\"form-group\">\n              <label htmlFor=\"nameOnDisc\">Name on Disc</label>\n              <input\n                type=\"text\"\n                id=\"nameOnDisc\"\n                name=\"nameOnDisc\"\n                value={formData.nameOnDisc}\n                onChange={handleInputChange}\n                placeholder=\"Name written on disc\"\n              />\n            </div>\n          </div>\n        </div>\n\n        <div className=\"form-section\">\n          <h3>Location & Date</h3>\n          <div className=\"form-row\">\n            <div className=\"form-group\">\n              <label htmlFor=\"locationFound\">Location Found *</label>\n              <input\n                type=\"text\"\n                id=\"locationFound\"\n                name=\"locationFound\"\n                value={formData.locationFound}\n                onChange={handleInputChange}\n                required\n                placeholder=\"e.g., Maple Hill Disc Golf Course, Hole 7\"\n              />\n            </div>\n            <div className=\"form-group\">\n              <label htmlFor=\"foundDate\">Date Found *</label>\n              <input\n                type=\"date\"\n                id=\"foundDate\"\n                name=\"foundDate\"\n                value={formData.foundDate}\n                onChange={handleInputChange}\n                required\n              />\n            </div>\n          </div>\n\n          <div className=\"form-group\">\n            <label htmlFor=\"description\">Additional Description</label>\n            <textarea\n              id=\"description\"\n              name=\"description\"\n              value={formData.description}\n              onChange={handleInputChange}\n              rows={4}\n              placeholder=\"Any additional details about where or how you found the disc...\"\n            />\n          </div>\n        </div>\n\n        <div className=\"form-section\">\n          <h3>Disc Images</h3>\n          <p className=\"form-section-description\">\n            Adding photos helps disc owners identify their disc more easily. You can upload up to 2 images.\n          </p>\n          <ImageUpload\n            onImagesChange={setSelectedImages}\n            maxImages={2}\n            maxSizePerImage={10}\n            disabled={isSubmitting}\n          />\n        </div>\n\n        <div className=\"form-actions\">\n          <button\n            type=\"button\"\n            className=\"button secondary\"\n            onClick={() => onNavigate('home')}\n            disabled={isSubmitting}\n          >\n            Cancel\n          </button>\n          <button\n            type=\"submit\"\n            className=\"button primary\"\n            disabled={isSubmitting || !user}\n          >\n            {isSubmitting ? 'Submitting...' : !user ? 'Sign In Required' : 'Report Found Disc'}\n          </button>\n        </div>\n      </form>\n    </div>\n  );\n}\n\nfunction SearchLost({ onNavigate }: PageProps) {\n  const { user, isGuest, userRole } = useAuth();\n  const [searchCriteria, setSearchCriteria] = useState({\n    brand: '',\n    mold: '',\n    color: '',\n    discType: '',\n    locationFound: '',\n  });\n  const [foundDiscs, setFoundDiscs] = useState<any[]>([]);\n  const [isSearching, setIsSearching] = useState(false);\n  const [hasSearched, setHasSearched] = useState(false);\n\n  const handleReturnStatusUpdate = (discId: string, newStatus: ReturnStatus) => {\n    // Update the disc in the local state\n    setFoundDiscs(prev => prev.map(disc =>\n      disc.id === discId\n        ? { ...disc, return_status: newStatus, returned_at: new Date().toISOString() }\n        : disc\n    ));\n  };\n\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {\n    const { name, value } = e.target;\n    setSearchCriteria(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n\n  const handleSearch = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setIsSearching(true);\n    setHasSearched(true);\n\n    try {\n      const { data, error } = await discService.searchFoundDiscs(searchCriteria);\n\n      if (error) {\n        console.error('Search error:', error);\n        setFoundDiscs([]);\n      } else {\n        console.log('Search results:', data);\n        setFoundDiscs(data || []);\n      }\n    } catch (error) {\n      console.error('Search failed:', error);\n      setFoundDiscs([]);\n    } finally {\n      setIsSearching(false);\n    }\n  };\n\n  const loadAllDiscs = async () => {\n    setIsSearching(true);\n    setHasSearched(true);\n\n    try {\n      const { data, error } = await discService.getFoundDiscs();\n\n      if (error) {\n        console.error('Load error:', error);\n        setFoundDiscs([]);\n      } else {\n        console.log('Load all results:', data);\n        setFoundDiscs(data || []);\n      }\n    } catch (error) {\n      console.error('Load failed:', error);\n      setFoundDiscs([]);\n    } finally {\n      setIsSearching(false);\n    }\n  };\n\n  return (\n    <div className=\"page-container\">\n      <div className=\"page-header\">\n        <button className=\"back-button\" onClick={() => onNavigate('home')}>\n          ← Back to Home\n        </button>\n        <h1>Search Lost Discs</h1>\n        <p>Search through reported found discs to see if someone has found your lost disc.</p>\n      </div>\n\n      <div className=\"search-container\">\n        <form className=\"search-form\" onSubmit={handleSearch}>\n          <div className=\"search-section\">\n            <h3>Search Criteria</h3>\n            <div className=\"form-row\">\n              <div className=\"form-group\">\n                <label htmlFor=\"search-brand\">Brand</label>\n                <input\n                  type=\"text\"\n                  id=\"search-brand\"\n                  name=\"brand\"\n                  value={searchCriteria.brand}\n                  onChange={handleInputChange}\n                  placeholder=\"e.g., Innova, Discraft\"\n                />\n              </div>\n              <div className=\"form-group\">\n                <label htmlFor=\"search-mold\">Mold</label>\n                <input\n                  type=\"text\"\n                  id=\"search-mold\"\n                  name=\"mold\"\n                  value={searchCriteria.mold}\n                  onChange={handleInputChange}\n                  placeholder=\"e.g., Destroyer, Buzzz\"\n                />\n              </div>\n            </div>\n\n            <div className=\"form-row\">\n              <div className=\"form-group\">\n                <label htmlFor=\"search-color\">Color</label>\n                <input\n                  type=\"text\"\n                  id=\"search-color\"\n                  name=\"color\"\n                  value={searchCriteria.color}\n                  onChange={handleInputChange}\n                  placeholder=\"e.g., Blue, Red\"\n                />\n              </div>\n              <div className=\"form-group\">\n                <label htmlFor=\"search-discType\">Disc Type</label>\n                <select\n                  id=\"search-discType\"\n                  name=\"discType\"\n                  value={searchCriteria.discType}\n                  onChange={handleInputChange}\n                >\n                  <option value=\"\">Any type</option>\n                  <option value=\"putter\">Putter</option>\n                  <option value=\"midrange\">Midrange</option>\n                  <option value=\"fairway_driver\">Fairway Driver</option>\n                  <option value=\"distance_driver\">Distance Driver</option>\n                  <option value=\"approach\">Approach</option>\n                </select>\n              </div>\n            </div>\n\n            <div className=\"form-group\">\n              <label htmlFor=\"search-location\">Location</label>\n              <input\n                type=\"text\"\n                id=\"search-location\"\n                name=\"locationFound\"\n                value={searchCriteria.locationFound}\n                onChange={handleInputChange}\n                placeholder=\"e.g., Maple Hill, DeLaveaga\"\n              />\n            </div>\n          </div>\n\n          <div className=\"search-actions\">\n            <button\n              type=\"button\"\n              className=\"button secondary\"\n              onClick={loadAllDiscs}\n              disabled={isSearching}\n            >\n              {isSearching ? 'Loading...' : 'Show All Found Discs'}\n            </button>\n            <button\n              type=\"submit\"\n              className=\"button primary\"\n              disabled={isSearching}\n            >\n              {isSearching ? 'Searching...' : 'Search'}\n            </button>\n          </div>\n        </form>\n\n        {hasSearched && (\n          <div className=\"search-results\">\n            <h3>\n              {foundDiscs.length > 0\n                ? `Found ${foundDiscs.length} disc${foundDiscs.length === 1 ? '' : 's'}`\n                : 'No discs found matching your criteria'\n              }\n            </h3>\n\n            {foundDiscs.length > 0 && (\n              <div className=\"disc-grid\">\n                {foundDiscs.map((disc) => (\n                  <div key={disc.id} className=\"disc-card\">\n                    <div className=\"disc-header\">\n                      <h4>{disc.brand} {disc.mold || 'Unknown Mold'}</h4>\n                      <span className=\"disc-type\">{disc.disc_type || 'Unknown Type'}</span>\n                    </div>\n\n                    {/* Return Status - only show for admin or if not 'Found' */}\n                    {(userRole === 'admin' || (disc.return_status && disc.return_status !== 'Found')) && (\n                      <ReturnStatusManager\n                        discId={disc.id}\n                        currentStatus={disc.return_status || 'Found'}\n                        onStatusUpdated={(newStatus) => handleReturnStatusUpdate(disc.id, newStatus)}\n                        disabled={userRole !== 'admin'}\n                      />\n                    )}\n\n                    {disc.image_urls && disc.image_urls.length > 0 && (\n                      <div className=\"disc-images\">\n                        {disc.image_urls.slice(0, 2).map((imageUrl: string, index: number) => (\n                          <img\n                            key={index}\n                            src={imageUrl}\n                            alt={`${disc.brand} ${disc.mold || 'disc'} ${index + 1}`}\n                            className=\"disc-image\"\n                            onError={(e) => {\n                              // Hide broken images\n                              (e.target as HTMLImageElement).style.display = 'none';\n                            }}\n                          />\n                        ))}\n                      </div>\n                    )}\n\n                    <div className=\"disc-details\">\n                      <div className=\"detail-row\">\n                        <span className=\"label\">Color:</span>\n                        <span className=\"value\">{disc.color}</span>\n                      </div>\n\n                      {disc.weight && (\n                        <div className=\"detail-row\">\n                          <span className=\"label\">Weight:</span>\n                          <span className=\"value\">{disc.weight}g</span>\n                        </div>\n                      )}\n\n                      <div className=\"detail-row\">\n                        <span className=\"label\">Condition:</span>\n                        <span className=\"value\">{disc.condition || 'Unknown'}</span>\n                      </div>\n\n                      <div className=\"detail-row\">\n                        <span className=\"label\">Found at:</span>\n                        <span className=\"value\">{disc.location_found}</span>\n                      </div>\n\n                      <div className=\"detail-row\">\n                        <span className=\"label\">Found on:</span>\n                        <span className=\"value\">{new Date(disc.found_date).toLocaleDateString()}</span>\n                      </div>\n\n                      {disc.phone_number && (\n                        <div className=\"detail-row\">\n                          <span className=\"label\">Phone on disc:</span>\n                          <span className=\"value\">{disc.phone_number}</span>\n                        </div>\n                      )}\n\n                      {disc.name_on_disc && (\n                        <div className=\"detail-row\">\n                          <span className=\"label\">Name on disc:</span>\n                          <span className=\"value\">{disc.name_on_disc}</span>\n                        </div>\n                      )}\n\n                      {disc.description && (\n                        <div className=\"detail-row\">\n                          <span className=\"label\">Description:</span>\n                          <span className=\"value\">{disc.description}</span>\n                        </div>\n                      )}\n                    </div>\n\n                    <div className=\"disc-actions\">\n                      <button className=\"button primary small\">\n                        Contact Finder\n                      </button>\n                      <button className=\"button secondary small\">\n                        Report as Mine\n                      </button>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            )}\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}\n\nfunction Login({ onNavigate }: PageProps) {\n  const { signIn, signUp } = useAuth();\n  const [isLogin, setIsLogin] = useState(true);\n  const [formData, setFormData] = useState({\n    email: '',\n    password: '',\n    fullName: '',\n    confirmPassword: ''\n  });\n  const [loading, setLoading] = useState(false);\n  const [message, setMessage] = useState('');\n\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setLoading(true);\n    setMessage('');\n\n    try {\n      if (isLogin) {\n        const { error } = await signIn(formData.email, formData.password);\n        if (error) {\n          setMessage(error.message);\n        } else {\n          setMessage('Signed in successfully!');\n          setTimeout(() => onNavigate('home'), 1000);\n        }\n      } else {\n        if (formData.password !== formData.confirmPassword) {\n          setMessage('Passwords do not match');\n          setLoading(false);\n          return;\n        }\n        if (formData.password.length < 6) {\n          setMessage('Password must be at least 6 characters');\n          setLoading(false);\n          return;\n        }\n\n        const { error } = await signUp(formData.email, formData.password, formData.fullName);\n        if (error) {\n          setMessage(error.message);\n        } else {\n          setMessage('Account created! Please check your email to verify your account.');\n        }\n      }\n    } catch (error) {\n      setMessage('An unexpected error occurred');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <div className=\"page-container\">\n      <div className=\"page-header\">\n        <button className=\"back-button\" onClick={() => onNavigate('home')}>\n          ← Back to Home\n        </button>\n        <h1>{isLogin ? 'Sign In' : 'Create Account'}</h1>\n        <p>{isLogin ? 'Sign in to your account' : 'Create an account to report and search for discs'}</p>\n      </div>\n\n      <div className=\"auth-container\">\n        <div className=\"auth-tabs\">\n          <button\n            className={`auth-tab ${isLogin ? 'active' : ''}`}\n            onClick={() => setIsLogin(true)}\n          >\n            Sign In\n          </button>\n          <button\n            className={`auth-tab ${!isLogin ? 'active' : ''}`}\n            onClick={() => setIsLogin(false)}\n          >\n            Sign Up\n          </button>\n        </div>\n\n        {message && (\n          <div className={`status-message ${message.includes('error') || message.includes('Error') ? 'error' : 'success'}`}>\n            {message}\n          </div>\n        )}\n\n        <form className=\"auth-form\" onSubmit={handleSubmit}>\n          {!isLogin && (\n            <div className=\"form-group\">\n              <label htmlFor=\"fullName\">Full Name</label>\n              <input\n                type=\"text\"\n                id=\"fullName\"\n                name=\"fullName\"\n                value={formData.fullName}\n                onChange={handleInputChange}\n                required={!isLogin}\n                placeholder=\"Your full name\"\n              />\n            </div>\n          )}\n\n          <div className=\"form-group\">\n            <label htmlFor=\"email\">Email</label>\n            <input\n              type=\"email\"\n              id=\"email\"\n              name=\"email\"\n              value={formData.email}\n              onChange={handleInputChange}\n              required\n              placeholder=\"<EMAIL>\"\n            />\n          </div>\n\n          <div className=\"form-group\">\n            <label htmlFor=\"password\">Password</label>\n            <input\n              type=\"password\"\n              id=\"password\"\n              name=\"password\"\n              value={formData.password}\n              onChange={handleInputChange}\n              required\n              placeholder={isLogin ? \"Your password\" : \"At least 6 characters\"}\n            />\n          </div>\n\n          {!isLogin && (\n            <div className=\"form-group\">\n              <label htmlFor=\"confirmPassword\">Confirm Password</label>\n              <input\n                type=\"password\"\n                id=\"confirmPassword\"\n                name=\"confirmPassword\"\n                value={formData.confirmPassword}\n                onChange={handleInputChange}\n                required={!isLogin}\n                placeholder=\"Confirm your password\"\n              />\n            </div>\n          )}\n\n          <button\n            type=\"submit\"\n            className=\"button primary full-width\"\n            disabled={loading}\n          >\n            {loading ? 'Please wait...' : (isLogin ? 'Sign In' : 'Create Account')}\n          </button>\n        </form>\n      </div>\n    </div>\n  );\n}\n\nexport default App;\n"], "mappings": ";;;;;;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,WAAW,EAAEC,YAAY,QAAsB,gBAAgB;AACxE,SAASC,YAAY,EAAEC,OAAO,QAAQ,wBAAwB;AAC9D,SAASC,WAAW,QAAQ,0BAA0B;AACtD,SAASC,mBAAmB,QAAQ,kCAAkC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAIvE,SAASC,UAAUA,CAAA,EAAG;EAAAC,EAAA;EACpB,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGZ,QAAQ,CAAO,MAAM,CAAC;EAC5D,MAAM;IAAEa,IAAI;IAAEC,QAAQ;IAAEC,OAAO;IAAEC;EAAQ,CAAC,GAAGZ,OAAO,CAAC,CAAC;EAEtD,MAAMa,UAAU,GAAGA,CAAA,KAAM;IACvB,QAAQN,WAAW;MACjB,KAAK,MAAM;QACT,oBAAOH,OAAA,CAACU,IAAI;UAACC,UAAU,EAAEP;QAAe;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC7C,KAAK,cAAc;QACjB,oBAAOf,OAAA,CAACgB,WAAW;UAACL,UAAU,EAAEP;QAAe;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACpD,KAAK,aAAa;QAChB,oBAAOf,OAAA,CAACiB,UAAU;UAACN,UAAU,EAAEP;QAAe;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACnD,KAAK,OAAO;QACV,oBAAOf,OAAA,CAACkB,KAAK;UAACP,UAAU,EAAEP;QAAe;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC9C,KAAK,OAAO;QACV,oBAAOf,OAAA,CAACmB,cAAc;UAACR,UAAU,EAAEP;QAAe;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACvD;QACE,oBAAOf,OAAA,CAACU,IAAI;UAACC,UAAU,EAAEP;QAAe;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;IAC/C;EACF,CAAC;EAED,MAAMK,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,MAAMb,OAAO,CAAC,CAAC;IACfH,cAAc,CAAC,MAAM,CAAC;EACxB,CAAC;EAED,IAAII,OAAO,EAAE;IACX,oBACER,OAAA;MAAKqB,SAAS,EAAC,KAAK;MAAAC,QAAA,eAClBtB,OAAA;QAAKqB,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChCtB,OAAA;UAAKqB,SAAS,EAAC;QAAiB;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACvCf,OAAA;UAAAsB,QAAA,EAAG;QAAU;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACjBf,OAAA;UAAGuB,KAAK,EAAE;YAAEC,QAAQ,EAAE,QAAQ;YAAEC,KAAK,EAAE,MAAM;YAAEC,SAAS,EAAE;UAAO,CAAE;UAAAJ,QAAA,EAAC;QAEpE;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACEf,OAAA;IAAKqB,SAAS,EAAC,KAAK;IAAAC,QAAA,gBAClBtB,OAAA;MAAKqB,SAAS,EAAC,QAAQ;MAAAC,QAAA,eACrBtB,OAAA;QAAKqB,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5BtB,OAAA;UAAKqB,SAAS,EAAC,MAAM;UAACM,OAAO,EAAEA,CAAA,KAAMvB,cAAc,CAAC,MAAM,CAAE;UAACmB,KAAK,EAAE;YAAEK,MAAM,EAAE;UAAU,CAAE;UAAAN,QAAA,EAAC;QAE3F;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACNf,OAAA;UAAKqB,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1BtB,OAAA;YAAQqB,SAAS,EAAC,YAAY;YAACM,OAAO,EAAEA,CAAA,KAAMvB,cAAc,CAAC,cAAc,CAAE;YAAAkB,QAAA,EAAC;UAE9E;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTf,OAAA;YAAQqB,SAAS,EAAC,YAAY;YAACM,OAAO,EAAEA,CAAA,KAAMvB,cAAc,CAAC,aAAa,CAAE;YAAAkB,QAAA,EAAC;UAE7E;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,EAERV,IAAI,gBACHL,OAAA;YAAKqB,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBtB,OAAA;cAAMqB,SAAS,EAAC,WAAW;cAAAC,QAAA,GACxBjB,IAAI,CAACwB,KAAK,EAAC,IAAE,EAACvB,QAAQ,EAAC,GAC1B;YAAA;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,EACNT,QAAQ,KAAK,OAAO,iBACnBN,OAAA;cAAQqB,SAAS,EAAC,YAAY;cAACM,OAAO,EAAEA,CAAA,KAAMvB,cAAc,CAAC,OAAO,CAAE;cAAAkB,QAAA,EAAC;YAEvE;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CACT,eACDf,OAAA;cAAQqB,SAAS,EAAC,YAAY;cAACM,OAAO,EAAEP,aAAc;cAAAE,QAAA,EAAC;YAEvD;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,gBAENf,OAAA;YAAQqB,SAAS,EAAC,oBAAoB;YAACM,OAAO,EAAEA,CAAA,KAAMvB,cAAc,CAAC,OAAO,CAAE;YAAAkB,QAAA,EAAC;UAE/E;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CACT;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENf,OAAA;MAAMqB,SAAS,EAAC,gBAAgB;MAAAC,QAAA,EAC7Bb,UAAU,CAAC;IAAC;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACT,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV;AAACb,EAAA,CAnFQD,UAAU;EAAA,QAE4BL,OAAO;AAAA;AAAAkC,EAAA,GAF7C7B,UAAU;AAqFnB,SAAS8B,GAAGA,CAAA,EAAG;EACb,oBACE/B,OAAA,CAACL,YAAY;IAAA2B,QAAA,eACXtB,OAAA,CAACC,UAAU;MAAAW,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAEnB;AAACiB,GAAA,GANQD,GAAG;AAYZ,SAASZ,cAAcA,CAAC;EAAER;AAAsB,CAAC,EAAE;EAAAsB,GAAA;EACjD,MAAM;IAAE3B;EAAS,CAAC,GAAGV,OAAO,CAAC,CAAC;EAC9B,MAAM,CAACsC,QAAQ,EAAEC,WAAW,CAAC,GAAG3C,QAAQ,CAAQ,EAAE,CAAC;EACnD,MAAM,CAAC4C,SAAS,EAAEC,YAAY,CAAC,GAAG7C,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC8C,MAAM,EAAEC,SAAS,CAAC,GAAG/C,QAAQ,CAAuB,KAAK,CAAC;EAEjE,MAAMgD,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/BH,YAAY,CAAC,IAAI,CAAC;IAClB,IAAI;MACF,MAAM;QAAEI,IAAI;QAAEC;MAAM,CAAC,GAAG,MAAMjD,WAAW,CAACkD,kBAAkB,CAAC,CAAC;MAC9D,IAAID,KAAK,EAAE;QACTE,OAAO,CAACF,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MACpD,CAAC,MAAM;QACLE,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEJ,IAAI,CAAC;QACxCN,WAAW,CAACM,IAAI,IAAI,EAAE,CAAC;MACzB;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdE,OAAO,CAACF,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;IACpD,CAAC,SAAS;MACRL,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED9C,KAAK,CAACuD,SAAS,CAAC,MAAM;IACpBN,YAAY,CAAC,CAAC;EAChB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMO,wBAAwB,GAAGA,CAACC,MAAc,EAAEC,SAAuB,KAAK;IAC5Ed,WAAW,CAACe,IAAI,IAAIA,IAAI,CAACC,GAAG,CAACC,IAAI,IAC/BA,IAAI,CAACC,EAAE,KAAKL,MAAM,GACd;MAAE,GAAGI,IAAI;MAAEE,aAAa,EAAEL,SAAS;MAAEM,WAAW,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;IAAE,CAAC,GAC5EL,IACN,CAAC,CAAC;EACJ,CAAC;EAED,MAAMM,aAAa,GAAGpB,MAAM,KAAK,KAAK,GAClCJ,QAAQ,GACRA,QAAQ,CAACI,MAAM,CAACc,IAAI,IAAIA,IAAI,CAACE,aAAa,KAAKhB,MAAM,CAAC;EAE1D,MAAMqB,YAAY,GAAGzB,QAAQ,CAAC0B,MAAM,CAAC,CAACC,MAAM,EAAET,IAAI,KAAK;IACrD,MAAMU,MAAM,GAAGV,IAAI,CAACE,aAAa,IAAI,OAAO;IAC5CO,MAAM,CAACC,MAAM,CAAC,GAAG,CAACD,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC;IAC1C,OAAOD,MAAM;EACf,CAAC,EAAE,CAAC,CAA2B,CAAC;;EAEhC;EACA,IAAIvD,QAAQ,KAAK,OAAO,EAAE;IACxB,oBACEN,OAAA;MAAKqB,SAAS,EAAC,gBAAgB;MAAAC,QAAA,eAC7BtB,OAAA;QAAKqB,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BtB,OAAA;UAAQqB,SAAS,EAAC,aAAa;UAACM,OAAO,EAAEA,CAAA,KAAMhB,UAAU,CAAC,MAAM,CAAE;UAAAW,QAAA,EAAC;QAEnE;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTf,OAAA;UAAAsB,QAAA,EAAI;QAAa;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACtBf,OAAA;UAAAsB,QAAA,EAAG;QAA8C;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACEf,OAAA;IAAKqB,SAAS,EAAC,gBAAgB;IAAAC,QAAA,gBAC7BtB,OAAA;MAAKqB,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1BtB,OAAA;QAAQqB,SAAS,EAAC,aAAa;QAACM,OAAO,EAAEA,CAAA,KAAMhB,UAAU,CAAC,MAAM,CAAE;QAAAW,QAAA,EAAC;MAEnE;QAAAV,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTf,OAAA;QAAAsB,QAAA,EAAI;MAAe;QAAAV,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACxBf,OAAA;QAAAsB,QAAA,EAAG;MAA+C;QAAAV,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnD,CAAC,eAGNf,OAAA;MAAKqB,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC7BtB,OAAA;QAAAsB,QAAA,EAAI;MAAc;QAAAV,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACvBf,OAAA;QAAKqB,SAAS,EAAC,eAAe;QAAAC,QAAA,EAC3ByC,MAAM,CAACC,OAAO,CAACL,YAAY,CAAC,CAACR,GAAG,CAAC,CAAC,CAACW,MAAM,EAAEG,KAAK,CAAC,kBAChDjE,OAAA;UAAkBqB,SAAS,EAAC,cAAc;UAAAC,QAAA,gBACxCtB,OAAA;YAAMqB,SAAS,EAAC,OAAO;YAAAC,QAAA,EAAE2C;UAAK;YAAArD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAiB,CAAC,eAChDf,OAAA;YAAMqB,SAAS,EAAC,QAAQ;YAAAC,QAAA,EAAEwC;UAAM;YAAAlD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA,GAFhC+C,MAAM;UAAAlD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAGX,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNf,OAAA;MAAKqB,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBAC9BtB,OAAA;QAAOkE,OAAO,EAAC,eAAe;QAAA5C,QAAA,EAAC;MAAiB;QAAAV,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACxDf,OAAA;QACEqD,EAAE,EAAC,eAAe;QAClBc,KAAK,EAAE7B,MAAO;QACd8B,QAAQ,EAAGC,CAAC,IAAK9B,SAAS,CAAC8B,CAAC,CAACC,MAAM,CAACH,KAA6B,CAAE;QAAA7C,QAAA,gBAEnEtB,OAAA;UAAQmE,KAAK,EAAC,KAAK;UAAA7C,QAAA,EAAC;QAAY;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACzCf,OAAA;UAAQmE,KAAK,EAAC,OAAO;UAAA7C,QAAA,EAAC;QAAK;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACpCf,OAAA;UAAQmE,KAAK,EAAC,mBAAmB;UAAA7C,QAAA,EAAC;QAAiB;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAC5Df,OAAA;UAAQmE,KAAK,EAAC,SAAS;UAAA7C,QAAA,EAAC;QAAO;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACxCf,OAAA;UAAQmE,KAAK,EAAC,MAAM;UAAA7C,QAAA,EAAC;QAAI;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAClCf,OAAA;UAAQmE,KAAK,EAAC,SAAS;UAAA7C,QAAA,EAAC;QAAO;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,EAGLqB,SAAS,gBACRpC,OAAA;MAAKqB,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAChCtB,OAAA;QAAKqB,SAAS,EAAC;MAAiB;QAAAT,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACvCf,OAAA;QAAAsB,QAAA,EAAG;MAAgB;QAAAV,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpB,CAAC,gBAENf,OAAA;MAAKqB,SAAS,EAAC,iBAAiB;MAAAC,QAAA,EAC7BoC,aAAa,CAACa,MAAM,KAAK,CAAC,gBACzBvE,OAAA;QAAAsB,QAAA,EAAG;MAAuC;QAAAV,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,GAE9C2C,aAAa,CAACP,GAAG,CAAEC,IAAI,iBACrBpD,OAAA;QAAmBqB,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC5CtB,OAAA;UAAKqB,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1BtB,OAAA;YAAAsB,QAAA,GAAK8B,IAAI,CAACoB,KAAK,EAAC,GAAC,EAACpB,IAAI,CAACqB,IAAI,IAAI,cAAc;UAAA;YAAA7D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACnDf,OAAA;YAAMqB,SAAS,EAAC,WAAW;YAAAC,QAAA,EAAE8B,IAAI,CAACsB,SAAS,IAAI;UAAc;YAAA9D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClE,CAAC,eAENf,OAAA,CAACF,mBAAmB;UAClBkD,MAAM,EAAEI,IAAI,CAACC,EAAG;UAChBsB,aAAa,EAAEvB,IAAI,CAACE,aAAa,IAAI,OAAQ;UAC7CsB,eAAe,EAAG3B,SAAS,IAAKF,wBAAwB,CAACK,IAAI,CAACC,EAAE,EAAEJ,SAAS,CAAE;UAC7E4B,QAAQ,EAAE;QAAM;UAAAjE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CAAC,EAEDqC,IAAI,CAAC0B,UAAU,IAAI1B,IAAI,CAAC0B,UAAU,CAACP,MAAM,GAAG,CAAC,iBAC5CvE,OAAA;UAAKqB,SAAS,EAAC,aAAa;UAAAC,QAAA,EACzB8B,IAAI,CAAC0B,UAAU,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC5B,GAAG,CAAC,CAAC6B,QAAgB,EAAEC,KAAa,kBAC/DjF,OAAA;YAEEkF,GAAG,EAAEF,QAAS;YACdG,GAAG,EAAE,GAAG/B,IAAI,CAACoB,KAAK,IAAIpB,IAAI,CAACqB,IAAI,IAAI,MAAM,IAAIQ,KAAK,GAAG,CAAC,EAAG;YACzD5D,SAAS,EAAC,YAAY;YACtB+D,OAAO,EAAGf,CAAC,IAAK;cACbA,CAAC,CAACC,MAAM,CAAsB/C,KAAK,CAAC8D,OAAO,GAAG,MAAM;YACvD;UAAE,GANGJ,KAAK;YAAArE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAOX,CACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN,eAEDf,OAAA;UAAKqB,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BtB,OAAA;YAAKqB,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBtB,OAAA;cAAMqB,SAAS,EAAC,OAAO;cAAAC,QAAA,EAAC;YAAM;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACrCf,OAAA;cAAMqB,SAAS,EAAC,OAAO;cAAAC,QAAA,EAAE8B,IAAI,CAAC3B;YAAK;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxC,CAAC,eACNf,OAAA;YAAKqB,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBtB,OAAA;cAAMqB,SAAS,EAAC,OAAO;cAAAC,QAAA,EAAC;YAAe;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC9Cf,OAAA;cAAMqB,SAAS,EAAC,OAAO;cAAAC,QAAA,EAAE8B,IAAI,CAACkC;YAAc;cAAA1E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjD,CAAC,eACNf,OAAA;YAAKqB,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBtB,OAAA;cAAMqB,SAAS,EAAC,OAAO;cAAAC,QAAA,EAAC;YAAW;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC1Cf,OAAA;cAAMqB,SAAS,EAAC,OAAO;cAAAC,QAAA,EAAE,IAAIkC,IAAI,CAACJ,IAAI,CAACmC,UAAU,CAAC,CAACC,kBAAkB,CAAC;YAAC;cAAA5E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5E,CAAC,EACLqC,IAAI,CAACG,WAAW,iBACfvD,OAAA;YAAKqB,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBtB,OAAA;cAAMqB,SAAS,EAAC,OAAO;cAAAC,QAAA,EAAC;YAAc;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC7Cf,OAAA;cAAMqB,SAAS,EAAC,OAAO;cAAAC,QAAA,EAAE,IAAIkC,IAAI,CAACJ,IAAI,CAACG,WAAW,CAAC,CAACiC,kBAAkB,CAAC;YAAC;cAAA5E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7E,CACN,EACAqC,IAAI,CAACqC,cAAc,iBAClBzF,OAAA;YAAKqB,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBtB,OAAA;cAAMqB,SAAS,EAAC,OAAO;cAAAC,QAAA,EAAC;YAAM;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACrCf,OAAA;cAAMqB,SAAS,EAAC,OAAO;cAAAC,QAAA,EAAE8B,IAAI,CAACqC;YAAc;cAAA7E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjD,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA,GAtDEqC,IAAI,CAACC,EAAE;QAAAzC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAuDZ,CACN;IACF;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV;AAACkB,GAAA,CA9KQd,cAAc;EAAA,QACAvB,OAAO;AAAA;AAAA8F,GAAA,GADrBvE,cAAc;AAgLvB,SAAST,IAAIA,CAAC;EAAEC;AAAsB,CAAC,EAAE;EACvC,oBACEX,OAAA;IAAAsB,QAAA,gBACEtB,OAAA;MAAKqB,SAAS,EAAC,MAAM;MAAAC,QAAA,gBACnBtB,OAAA;QAAAsB,QAAA,EAAI;MAAe;QAAAV,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACxBf,OAAA;QAAAsB,QAAA,EAAG;MAGH;QAAAV,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eAEJf,OAAA;QAAKqB,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BtB,OAAA;UAAQqB,SAAS,EAAC,qBAAqB;UAACM,OAAO,EAAEA,CAAA,KAAMhB,UAAU,CAAC,cAAc,CAAE;UAAAW,QAAA,EAAC;QAEnF;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTf,OAAA;UAAQqB,SAAS,EAAC,uBAAuB;UAACM,OAAO,EAAEA,CAAA,KAAMhB,UAAU,CAAC,aAAa,CAAE;UAAAW,QAAA,EAAC;QAEpF;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENf,OAAA;MAAKqB,SAAS,EAAC,UAAU;MAAAC,QAAA,gBACvBtB,OAAA;QAAKqB,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BtB,OAAA;UAAKqB,SAAS,EAAC,cAAc;UAAAC,QAAA,eAC3BtB,OAAA;YAAAsB,QAAA,EAAK;UAAE;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eACNf,OAAA;UAAAsB,QAAA,EAAI;QAAc;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACvBf,OAAA;UAAAsB,QAAA,EAAG;QAEH;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAENf,OAAA;QAAKqB,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BtB,OAAA;UAAKqB,SAAS,EAAC,cAAc;UAAAC,QAAA,eAC3BtB,OAAA;YAAAsB,QAAA,EAAK;UAAE;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eACNf,OAAA;UAAAsB,QAAA,EAAI;QAAc;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACvBf,OAAA;UAAAsB,QAAA,EAAG;QAEH;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAENf,OAAA;QAAKqB,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BtB,OAAA;UAAKqB,SAAS,EAAC,cAAc;UAAAC,QAAA,eAC3BtB,OAAA;YAAAsB,QAAA,EAAK;UAAE;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eACNf,OAAA;UAAAsB,QAAA,EAAI;QAAkB;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC3Bf,OAAA;UAAAsB,QAAA,EAAG;QAEH;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENf,OAAA;MAAKqB,SAAS,EAAC,OAAO;MAAAC,QAAA,eACpBtB,OAAA;QAAKqB,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzBtB,OAAA;UAAKqB,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBtB,OAAA;YAAKqB,SAAS,EAAC,aAAa;YAAAC,QAAA,EAAC;UAAI;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACvCf,OAAA;YAAKqB,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAc;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7C,CAAC,eACNf,OAAA;UAAKqB,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBtB,OAAA;YAAKqB,SAAS,EAAC,aAAa;YAAAC,QAAA,EAAC;UAAM;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACzCf,OAAA;YAAKqB,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAY;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3C,CAAC,eACNf,OAAA;UAAKqB,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBtB,OAAA;YAAKqB,SAAS,EAAC,aAAa;YAAAC,QAAA,EAAC;UAAG;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACtCf,OAAA;YAAKqB,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAY;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENf,OAAA;MAAKqB,SAAS,EAAC,KAAK;MAAAC,QAAA,gBAClBtB,OAAA;QAAAsB,QAAA,EAAI;MAAkB;QAAAV,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC3Bf,OAAA;QAAAsB,QAAA,EAAG;MAEH;QAAAV,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eACJf,OAAA;QAAQqB,SAAS,EAAC,YAAY;QAACM,OAAO,EAAEA,CAAA,KAAMhB,UAAU,CAAC,OAAO,CAAE;QAAAW,QAAA,EAAC;MAEnE;QAAAV,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAAC4E,GAAA,GAhFQjF,IAAI;AAkFb,SAASM,WAAWA,CAAC;EAAEL;AAAsB,CAAC,EAAE;EAAAiF,GAAA;EAC9C,MAAM;IAAEvF,IAAI;IAAEwF,OAAO;IAAEvF;EAAS,CAAC,GAAGV,OAAO,CAAC,CAAC;EAC7C,MAAM,CAACkG,QAAQ,EAAEC,WAAW,CAAC,GAAGvG,QAAQ,CAAC;IACvCgF,KAAK,EAAE,EAAE;IACTC,IAAI,EAAE,EAAE;IACRuB,QAAQ,EAAE,EAAE;IACZvE,KAAK,EAAE,EAAE;IACTwE,MAAM,EAAE,EAAE;IACVC,SAAS,EAAE,EAAE;IACbC,WAAW,EAAE,EAAE;IACfC,SAAS,EAAE,EAAE;IACbC,WAAW,EAAE,EAAE;IACfC,UAAU,EAAE,EAAE;IACdC,aAAa,EAAE,EAAE;IACjBC,SAAS,EAAE,EAAE;IACbC,WAAW,EAAE;EACf,CAAC,CAAC;EACF,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGnH,QAAQ,CAAS,EAAE,CAAC;EAChE,MAAM,CAACoH,YAAY,EAAEC,eAAe,CAAC,GAAGrH,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACsH,aAAa,EAAEC,gBAAgB,CAAC,GAAGvH,QAAQ,CAAC,EAAE,CAAC;;EAEtD;EACA,IAAIqG,OAAO,EAAE;IACX,oBACE7F,OAAA;MAAKqB,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC7BtB,OAAA;QAAKqB,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BtB,OAAA;UAAQqB,SAAS,EAAC,aAAa;UAACM,OAAO,EAAEA,CAAA,KAAMhB,UAAU,CAAC,MAAM,CAAE;UAAAW,QAAA,EAAC;QAEnE;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTf,OAAA;UAAAsB,QAAA,EAAI;QAAmB;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC5Bf,OAAA;UAAAsB,QAAA,EAAG;QAA4C;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChD,CAAC,eACNf,OAAA;QAAKqB,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5BtB,OAAA;UAAAsB,QAAA,EAAI;QAAuB;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAChCf,OAAA;UAAAsB,QAAA,EAAG;QAAqI;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAC5If,OAAA;UAAQqB,SAAS,EAAC,gBAAgB;UAACM,OAAO,EAAEA,CAAA,KAAMhB,UAAU,CAAC,OAAO,CAAE;UAAAW,QAAA,EAAC;QAEvE;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,MAAMiG,iBAAiB,GAAI3C,CAAgF,IAAK;IAC9G,MAAM;MAAE4C,IAAI;MAAE9C;IAAM,CAAC,GAAGE,CAAC,CAACC,MAAM;IAChCyB,WAAW,CAAC7C,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAAC+D,IAAI,GAAG9C;IACV,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAM+C,YAAY,GAAG,MAAO7C,CAAkB,IAAK;IACjDA,CAAC,CAAC8C,cAAc,CAAC,CAAC;IAClBN,eAAe,CAAC,IAAI,CAAC;IACrBE,gBAAgB,CAAC,EAAE,CAAC;IAEpB,IAAI;MACFnE,OAAO,CAACC,GAAG,CAAC,yBAAyB,CAAC;MACtCD,OAAO,CAACC,GAAG,CAAC,OAAO,EAAExC,IAAI,CAAC;MAC1BuC,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAE6D,cAAc,CAACnC,MAAM,CAAC;;MAEtD;MACA,IAAImC,cAAc,CAACnC,MAAM,GAAG,CAAC,IAAI,CAAClE,IAAI,EAAE;QACtC0G,gBAAgB,CAAC,+CAA+C,CAAC;QACjE;MACF;;MAEA;MACA,MAAM;QAAEK;MAAU,CAAC,GAAG,MAAM3H,WAAW,CAAC4H,cAAc,CAAC,CAAC;MACxDzE,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEuE,SAAS,CAAC;MAEnD,IAAI,CAACA,SAAS,EAAE;QACdL,gBAAgB,CAAC,kEAAkE,CAAC;QACpFnE,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEiD,QAAQ,EAAE,SAAS,EAAEY,cAAc,CAACnC,MAAM,CAAC;QACrE+C,UAAU,CAAC,MAAM;UACf3G,UAAU,CAAC,MAAM,CAAC;QACpB,CAAC,EAAE,IAAI,CAAC;QACR;MACF;MAEA,IAAI4G,SAAmB,GAAG,EAAE;;MAE5B;MACA,IAAIb,cAAc,CAACnC,MAAM,GAAG,CAAC,EAAE;QAC7B3B,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAExC,IAAI,CAAEgD,EAAE,CAAC;QACxD0D,gBAAgB,CAAC,qBAAqB,CAAC;QACvC,MAAM;UAAES,IAAI;UAAE9E,KAAK,EAAE+E;QAAW,CAAC,GAAG,MAAM/H,YAAY,CAACgI,YAAY,CAAChB,cAAc,EAAErG,IAAI,CAAEgD,EAAE,CAAC;QAE7FT,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE;UAAE2E,IAAI;UAAE9E,KAAK,EAAE+E;QAAW,CAAC,CAAC;QAEhE,IAAIA,UAAU,EAAE;UACd7E,OAAO,CAACF,KAAK,CAAC,qBAAqB,EAAE+E,UAAU,CAAC;UAChDV,gBAAgB,CAAC,2BAA2BU,UAAU,CAACE,OAAO,IAAIC,IAAI,CAACC,SAAS,CAACJ,UAAU,CAAC,EAAE,CAAC;UAC/F;QACF;QAEAF,SAAS,GAAGC,IAAI;QAChB5E,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE0E,SAAS,CAAC;MACzD;;MAEA;MACA,MAAMO,QAAQ,GAAG;QACfC,SAAS,EAAE1H,IAAI,CAAEgD,EAAE;QAAE;QACrBmB,KAAK,EAAEsB,QAAQ,CAACtB,KAAK;QACrBC,IAAI,EAAEqB,QAAQ,CAACrB,IAAI,IAAIuD,SAAS;QAChCtD,SAAS,EAAEoB,QAAQ,CAACE,QAAQ,IAAIgC,SAAS;QACzCvG,KAAK,EAAEqE,QAAQ,CAACrE,KAAK;QACrBwE,MAAM,EAAEH,QAAQ,CAACG,MAAM,GAAGgC,QAAQ,CAACnC,QAAQ,CAACG,MAAM,CAAC,GAAG+B,SAAS;QAC/D9B,SAAS,EAAEJ,QAAQ,CAACI,SAAS,IAAI8B,SAAS;QAC1CE,YAAY,EAAEpC,QAAQ,CAACK,WAAW,IAAI6B,SAAS;QAC/CG,UAAU,EAAErC,QAAQ,CAACM,SAAS,IAAI4B,SAAS;QAC3CI,YAAY,EAAEtC,QAAQ,CAACO,WAAW,IAAI2B,SAAS;QAC/CK,YAAY,EAAEvC,QAAQ,CAACQ,UAAU,IAAI0B,SAAS;QAC9C1C,cAAc,EAAEQ,QAAQ,CAACS,aAAa;QACtChB,UAAU,EAAEO,QAAQ,CAACU,SAAS;QAC9BC,WAAW,EAAEX,QAAQ,CAACW,WAAW,IAAIuB,SAAS;QAC9ClD,UAAU,EAAEyC,SAAS,CAAChD,MAAM,GAAG,CAAC,GAAGgD,SAAS,GAAGS;MACjD,CAAC;MAEDjB,gBAAgB,CAAC,4BAA4B,CAAC;MAC9C,MAAM;QAAEtE,IAAI;QAAEC;MAAM,CAAC,GAAG,MAAMjD,WAAW,CAAC6I,eAAe,CAACR,QAAQ,CAAC;MAEnE,IAAIpF,KAAK,EAAE;QACT;QACA,IAAI6E,SAAS,CAAChD,MAAM,GAAG,CAAC,EAAE;UACxB,MAAM7E,YAAY,CAAC6I,YAAY,CAAChB,SAAS,CAAC;QAC5C;QACAR,gBAAgB,CAAC,UAAU,CAACrE,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAUiF,OAAO,KAAI,wBAAwB,EAAE,CAAC;MACnF,CAAC,MAAM;QACLZ,gBAAgB,CAAC,mCAAmC,CAAC;QACrDnE,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEJ,IAAI,CAAC;QAChC6E,UAAU,CAAC,MAAM;UACf3G,UAAU,CAAC,MAAM,CAAC;QACpB,CAAC,EAAE,IAAI,CAAC;MACV;IACF,CAAC,CAAC,OAAO+B,KAAK,EAAE;MACdqE,gBAAgB,CAAC,kEAAkE,CAAC;MACpFnE,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEiD,QAAQ,EAAE,SAAS,EAAEY,cAAc,CAACnC,MAAM,CAAC;MACrE+C,UAAU,CAAC,MAAM;QACf3G,UAAU,CAAC,MAAM,CAAC;MACpB,CAAC,EAAE,IAAI,CAAC;IACV,CAAC,SAAS;MACRkG,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;EAED,oBACE7G,OAAA;IAAKqB,SAAS,EAAC,gBAAgB;IAAAC,QAAA,gBAC7BtB,OAAA;MAAKqB,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1BtB,OAAA;QAAQqB,SAAS,EAAC,aAAa;QAACM,OAAO,EAAEA,CAAA,KAAMhB,UAAU,CAAC,MAAM,CAAE;QAAAW,QAAA,EAAC;MAEnE;QAAAV,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTf,OAAA;QAAAsB,QAAA,EAAI;MAAiB;QAAAV,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC1Bf,OAAA;QAAAsB,QAAA,EAAG;MAAiF;QAAAV,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrF,CAAC,EAEL,CAACV,IAAI,iBACJL,OAAA;MAAKqB,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1BtB,OAAA;QAAAsB,QAAA,gBACEtB,OAAA;UAAAsB,QAAA,EAAQ;QAAiB;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,sEACpC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eACJf,OAAA;QACEqB,SAAS,EAAC,gBAAgB;QAC1BM,OAAO,EAAEA,CAAA,KAAMhB,UAAU,CAAC,OAAO,CAAE;QAAAW,QAAA,EACpC;MAED;QAAAV,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CACN,EAEA+F,aAAa,iBACZ9G,OAAA;MAAKqB,SAAS,EAAE,kBAAkByF,aAAa,CAAC0B,QAAQ,CAAC,OAAO,CAAC,GAAG,OAAO,GAAG,SAAS,EAAG;MAAAlH,QAAA,EACvFwF;IAAa;MAAAlG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACX,CACN,eAEDf,OAAA;MAAMqB,SAAS,EAAC,WAAW;MAACoH,QAAQ,EAAEvB,YAAa;MAAC3F,KAAK,EAAE;QAAEmH,OAAO,EAAE,CAACrI,IAAI,GAAG,GAAG,GAAG;MAAE,CAAE;MAAAiB,QAAA,gBACtFtB,OAAA;QAAKqB,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BtB,OAAA;UAAAsB,QAAA,EAAI;QAAgB;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACzBf,OAAA;UAAKqB,SAAS,EAAC,UAAU;UAAAC,QAAA,gBACvBtB,OAAA;YAAKqB,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBtB,OAAA;cAAOkE,OAAO,EAAC,OAAO;cAAA5C,QAAA,EAAC;YAAO;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACtCf,OAAA;cACE2I,IAAI,EAAC,MAAM;cACXtF,EAAE,EAAC,OAAO;cACV4D,IAAI,EAAC,OAAO;cACZ9C,KAAK,EAAE2B,QAAQ,CAACtB,KAAM;cACtBJ,QAAQ,EAAE4C,iBAAkB;cAC5B4B,QAAQ;cACRC,WAAW,EAAC;YAAuC;cAAAjI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNf,OAAA;YAAKqB,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBtB,OAAA;cAAOkE,OAAO,EAAC,MAAM;cAAA5C,QAAA,EAAC;YAAM;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACpCf,OAAA;cACE2I,IAAI,EAAC,MAAM;cACXtF,EAAE,EAAC,MAAM;cACT4D,IAAI,EAAC,MAAM;cACX9C,KAAK,EAAE2B,QAAQ,CAACrB,IAAK;cACrBL,QAAQ,EAAE4C,iBAAkB;cAC5B4B,QAAQ;cACRC,WAAW,EAAC;YAA+B;cAAAjI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENf,OAAA;UAAKqB,SAAS,EAAC,UAAU;UAAAC,QAAA,gBACvBtB,OAAA;YAAKqB,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBtB,OAAA;cAAOkE,OAAO,EAAC,UAAU;cAAA5C,QAAA,EAAC;YAAS;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC3Cf,OAAA;cACE2I,IAAI,EAAC,MAAM;cACXtF,EAAE,EAAC,UAAU;cACb4D,IAAI,EAAC,UAAU;cACf9C,KAAK,EAAE2B,QAAQ,CAACE,QAAS;cACzB5B,QAAQ,EAAE4C,iBAAkB;cAC5B6B,WAAW,EAAC;YAAyD;cAAAjI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNf,OAAA;YAAKqB,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBtB,OAAA;cAAOkE,OAAO,EAAC,OAAO;cAAA5C,QAAA,EAAC;YAAO;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACtCf,OAAA;cACE2I,IAAI,EAAC,MAAM;cACXtF,EAAE,EAAC,OAAO;cACV4D,IAAI,EAAC,OAAO;cACZ9C,KAAK,EAAE2B,QAAQ,CAACrE,KAAM;cACtB2C,QAAQ,EAAE4C,iBAAkB;cAC5B4B,QAAQ;cACRC,WAAW,EAAC;YAAyB;cAAAjI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENf,OAAA;UAAKqB,SAAS,EAAC,UAAU;UAAAC,QAAA,gBACvBtB,OAAA;YAAKqB,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBtB,OAAA;cAAOkE,OAAO,EAAC,QAAQ;cAAA5C,QAAA,EAAC;YAAc;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC9Cf,OAAA;cACE2I,IAAI,EAAC,QAAQ;cACbtF,EAAE,EAAC,QAAQ;cACX4D,IAAI,EAAC,QAAQ;cACb9C,KAAK,EAAE2B,QAAQ,CAACG,MAAO;cACvB7B,QAAQ,EAAE4C,iBAAkB;cAC5B6B,WAAW,EAAC,WAAW;cACvBC,GAAG,EAAC,KAAK;cACTC,GAAG,EAAC;YAAK;cAAAnI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNf,OAAA;YAAKqB,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBtB,OAAA;cAAOkE,OAAO,EAAC,WAAW;cAAA5C,QAAA,EAAC;YAAS;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC5Cf,OAAA;cACE2I,IAAI,EAAC,MAAM;cACXtF,EAAE,EAAC,WAAW;cACd4D,IAAI,EAAC,WAAW;cAChB9C,KAAK,EAAE2B,QAAQ,CAACI,SAAU;cAC1B9B,QAAQ,EAAE4C,iBAAkB;cAC5B6B,WAAW,EAAC;YAAwC;cAAAjI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENf,OAAA;QAAKqB,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BtB,OAAA;UAAAsB,QAAA,EAAI;QAAkB;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC3Bf,OAAA;UAAKqB,SAAS,EAAC,UAAU;UAAAC,QAAA,gBACvBtB,OAAA;YAAKqB,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBtB,OAAA;cAAOkE,OAAO,EAAC,aAAa;cAAA5C,QAAA,EAAC;YAAY;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACjDf,OAAA;cACE2I,IAAI,EAAC,MAAM;cACXtF,EAAE,EAAC,aAAa;cAChB4D,IAAI,EAAC,aAAa;cAClB9C,KAAK,EAAE2B,QAAQ,CAACK,WAAY;cAC5B/B,QAAQ,EAAE4C,iBAAkB;cAC5B6B,WAAW,EAAC;YAA4B;cAAAjI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNf,OAAA;YAAKqB,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBtB,OAAA;cAAOkE,OAAO,EAAC,WAAW;cAAA5C,QAAA,EAAC;YAAU;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC7Cf,OAAA;cACE2I,IAAI,EAAC,MAAM;cACXtF,EAAE,EAAC,WAAW;cACd4D,IAAI,EAAC,WAAW;cAChB9C,KAAK,EAAE2B,QAAQ,CAACM,SAAU;cAC1BhC,QAAQ,EAAE4C,iBAAkB;cAC5B6B,WAAW,EAAC;YAAgC;cAAAjI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENf,OAAA;UAAKqB,SAAS,EAAC,UAAU;UAAAC,QAAA,gBACvBtB,OAAA;YAAKqB,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBtB,OAAA;cAAOkE,OAAO,EAAC,aAAa;cAAA5C,QAAA,EAAC;YAAoB;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACzDf,OAAA;cACE2I,IAAI,EAAC,KAAK;cACVtF,EAAE,EAAC,aAAa;cAChB4D,IAAI,EAAC,aAAa;cAClB9C,KAAK,EAAE2B,QAAQ,CAACO,WAAY;cAC5BjC,QAAQ,EAAE4C,iBAAkB;cAC5B6B,WAAW,EAAC;YAA8B;cAAAjI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNf,OAAA;YAAKqB,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBtB,OAAA;cAAOkE,OAAO,EAAC,YAAY;cAAA5C,QAAA,EAAC;YAAY;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAChDf,OAAA;cACE2I,IAAI,EAAC,MAAM;cACXtF,EAAE,EAAC,YAAY;cACf4D,IAAI,EAAC,YAAY;cACjB9C,KAAK,EAAE2B,QAAQ,CAACQ,UAAW;cAC3BlC,QAAQ,EAAE4C,iBAAkB;cAC5B6B,WAAW,EAAC;YAAsB;cAAAjI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENf,OAAA;QAAKqB,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BtB,OAAA;UAAAsB,QAAA,EAAI;QAAe;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACxBf,OAAA;UAAKqB,SAAS,EAAC,UAAU;UAAAC,QAAA,gBACvBtB,OAAA;YAAKqB,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBtB,OAAA;cAAOkE,OAAO,EAAC,eAAe;cAAA5C,QAAA,EAAC;YAAgB;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACvDf,OAAA;cACE2I,IAAI,EAAC,MAAM;cACXtF,EAAE,EAAC,eAAe;cAClB4D,IAAI,EAAC,eAAe;cACpB9C,KAAK,EAAE2B,QAAQ,CAACS,aAAc;cAC9BnC,QAAQ,EAAE4C,iBAAkB;cAC5B4B,QAAQ;cACRC,WAAW,EAAC;YAA2C;cAAAjI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNf,OAAA;YAAKqB,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBtB,OAAA;cAAOkE,OAAO,EAAC,WAAW;cAAA5C,QAAA,EAAC;YAAY;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC/Cf,OAAA;cACE2I,IAAI,EAAC,MAAM;cACXtF,EAAE,EAAC,WAAW;cACd4D,IAAI,EAAC,WAAW;cAChB9C,KAAK,EAAE2B,QAAQ,CAACU,SAAU;cAC1BpC,QAAQ,EAAE4C,iBAAkB;cAC5B4B,QAAQ;YAAA;cAAAhI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENf,OAAA;UAAKqB,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBtB,OAAA;YAAOkE,OAAO,EAAC,aAAa;YAAA5C,QAAA,EAAC;UAAsB;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC3Df,OAAA;YACEqD,EAAE,EAAC,aAAa;YAChB4D,IAAI,EAAC,aAAa;YAClB9C,KAAK,EAAE2B,QAAQ,CAACW,WAAY;YAC5BrC,QAAQ,EAAE4C,iBAAkB;YAC5BgC,IAAI,EAAE,CAAE;YACRH,WAAW,EAAC;UAAiE;YAAAjI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9E,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENf,OAAA;QAAKqB,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BtB,OAAA;UAAAsB,QAAA,EAAI;QAAW;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACpBf,OAAA;UAAGqB,SAAS,EAAC,0BAA0B;UAAAC,QAAA,EAAC;QAExC;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJf,OAAA,CAACH,WAAW;UACVoJ,cAAc,EAAEtC,iBAAkB;UAClCuC,SAAS,EAAE,CAAE;UACbC,eAAe,EAAE,EAAG;UACpBtE,QAAQ,EAAE+B;QAAa;UAAAhG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAENf,OAAA;QAAKqB,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BtB,OAAA;UACE2I,IAAI,EAAC,QAAQ;UACbtH,SAAS,EAAC,kBAAkB;UAC5BM,OAAO,EAAEA,CAAA,KAAMhB,UAAU,CAAC,MAAM,CAAE;UAClCkE,QAAQ,EAAE+B,YAAa;UAAAtF,QAAA,EACxB;QAED;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTf,OAAA;UACE2I,IAAI,EAAC,QAAQ;UACbtH,SAAS,EAAC,gBAAgB;UAC1BwD,QAAQ,EAAE+B,YAAY,IAAI,CAACvG,IAAK;UAAAiB,QAAA,EAE/BsF,YAAY,GAAG,eAAe,GAAG,CAACvG,IAAI,GAAG,kBAAkB,GAAG;QAAmB;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5E,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV;AAAC6E,GAAA,CAnYQ5E,WAAW;EAAA,QACkBpB,OAAO;AAAA;AAAAwJ,GAAA,GADpCpI,WAAW;AAqYpB,SAASC,UAAUA,CAAC;EAAEN;AAAsB,CAAC,EAAE;EAAA0I,GAAA;EAC7C,MAAM;IAAEhJ,IAAI;IAAEwF,OAAO;IAAEvF;EAAS,CAAC,GAAGV,OAAO,CAAC,CAAC;EAC7C,MAAM,CAAC0J,cAAc,EAAEC,iBAAiB,CAAC,GAAG/J,QAAQ,CAAC;IACnDgF,KAAK,EAAE,EAAE;IACTC,IAAI,EAAE,EAAE;IACRhD,KAAK,EAAE,EAAE;IACTuE,QAAQ,EAAE,EAAE;IACZO,aAAa,EAAE;EACjB,CAAC,CAAC;EACF,MAAM,CAACiD,UAAU,EAAEC,aAAa,CAAC,GAAGjK,QAAQ,CAAQ,EAAE,CAAC;EACvD,MAAM,CAACkK,WAAW,EAAEC,cAAc,CAAC,GAAGnK,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACoK,WAAW,EAAEC,cAAc,CAAC,GAAGrK,QAAQ,CAAC,KAAK,CAAC;EAErD,MAAMuD,wBAAwB,GAAGA,CAACC,MAAc,EAAEC,SAAuB,KAAK;IAC5E;IACAwG,aAAa,CAACvG,IAAI,IAAIA,IAAI,CAACC,GAAG,CAACC,IAAI,IACjCA,IAAI,CAACC,EAAE,KAAKL,MAAM,GACd;MAAE,GAAGI,IAAI;MAAEE,aAAa,EAAEL,SAAS;MAAEM,WAAW,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;IAAE,CAAC,GAC5EL,IACN,CAAC,CAAC;EACJ,CAAC;EAED,MAAM4D,iBAAiB,GAAI3C,CAA0D,IAAK;IACxF,MAAM;MAAE4C,IAAI;MAAE9C;IAAM,CAAC,GAAGE,CAAC,CAACC,MAAM;IAChCiF,iBAAiB,CAACrG,IAAI,KAAK;MACzB,GAAGA,IAAI;MACP,CAAC+D,IAAI,GAAG9C;IACV,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAM2F,YAAY,GAAG,MAAOzF,CAAkB,IAAK;IACjDA,CAAC,CAAC8C,cAAc,CAAC,CAAC;IAClBwC,cAAc,CAAC,IAAI,CAAC;IACpBE,cAAc,CAAC,IAAI,CAAC;IAEpB,IAAI;MACF,MAAM;QAAEpH,IAAI;QAAEC;MAAM,CAAC,GAAG,MAAMjD,WAAW,CAACsK,gBAAgB,CAACT,cAAc,CAAC;MAE1E,IAAI5G,KAAK,EAAE;QACTE,OAAO,CAACF,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;QACrC+G,aAAa,CAAC,EAAE,CAAC;MACnB,CAAC,MAAM;QACL7G,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEJ,IAAI,CAAC;QACpCgH,aAAa,CAAChH,IAAI,IAAI,EAAE,CAAC;MAC3B;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdE,OAAO,CAACF,KAAK,CAAC,gBAAgB,EAAEA,KAAK,CAAC;MACtC+G,aAAa,CAAC,EAAE,CAAC;IACnB,CAAC,SAAS;MACRE,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;EAED,MAAMnH,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/BmH,cAAc,CAAC,IAAI,CAAC;IACpBE,cAAc,CAAC,IAAI,CAAC;IAEpB,IAAI;MACF,MAAM;QAAEpH,IAAI;QAAEC;MAAM,CAAC,GAAG,MAAMjD,WAAW,CAACuK,aAAa,CAAC,CAAC;MAEzD,IAAItH,KAAK,EAAE;QACTE,OAAO,CAACF,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;QACnC+G,aAAa,CAAC,EAAE,CAAC;MACnB,CAAC,MAAM;QACL7G,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEJ,IAAI,CAAC;QACtCgH,aAAa,CAAChH,IAAI,IAAI,EAAE,CAAC;MAC3B;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdE,OAAO,CAACF,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;MACpC+G,aAAa,CAAC,EAAE,CAAC;IACnB,CAAC,SAAS;MACRE,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;EAED,oBACE3J,OAAA;IAAKqB,SAAS,EAAC,gBAAgB;IAAAC,QAAA,gBAC7BtB,OAAA;MAAKqB,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1BtB,OAAA;QAAQqB,SAAS,EAAC,aAAa;QAACM,OAAO,EAAEA,CAAA,KAAMhB,UAAU,CAAC,MAAM,CAAE;QAAAW,QAAA,EAAC;MAEnE;QAAAV,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTf,OAAA;QAAAsB,QAAA,EAAI;MAAiB;QAAAV,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC1Bf,OAAA;QAAAsB,QAAA,EAAG;MAA+E;QAAAV,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnF,CAAC,eAENf,OAAA;MAAKqB,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAC/BtB,OAAA;QAAMqB,SAAS,EAAC,aAAa;QAACoH,QAAQ,EAAEqB,YAAa;QAAAxI,QAAA,gBACnDtB,OAAA;UAAKqB,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BtB,OAAA;YAAAsB,QAAA,EAAI;UAAe;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACxBf,OAAA;YAAKqB,SAAS,EAAC,UAAU;YAAAC,QAAA,gBACvBtB,OAAA;cAAKqB,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBtB,OAAA;gBAAOkE,OAAO,EAAC,cAAc;gBAAA5C,QAAA,EAAC;cAAK;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC3Cf,OAAA;gBACE2I,IAAI,EAAC,MAAM;gBACXtF,EAAE,EAAC,cAAc;gBACjB4D,IAAI,EAAC,OAAO;gBACZ9C,KAAK,EAAEmF,cAAc,CAAC9E,KAAM;gBAC5BJ,QAAQ,EAAE4C,iBAAkB;gBAC5B6B,WAAW,EAAC;cAAwB;gBAAAjI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNf,OAAA;cAAKqB,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBtB,OAAA;gBAAOkE,OAAO,EAAC,aAAa;gBAAA5C,QAAA,EAAC;cAAI;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACzCf,OAAA;gBACE2I,IAAI,EAAC,MAAM;gBACXtF,EAAE,EAAC,aAAa;gBAChB4D,IAAI,EAAC,MAAM;gBACX9C,KAAK,EAAEmF,cAAc,CAAC7E,IAAK;gBAC3BL,QAAQ,EAAE4C,iBAAkB;gBAC5B6B,WAAW,EAAC;cAAwB;gBAAAjI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENf,OAAA;YAAKqB,SAAS,EAAC,UAAU;YAAAC,QAAA,gBACvBtB,OAAA;cAAKqB,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBtB,OAAA;gBAAOkE,OAAO,EAAC,cAAc;gBAAA5C,QAAA,EAAC;cAAK;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC3Cf,OAAA;gBACE2I,IAAI,EAAC,MAAM;gBACXtF,EAAE,EAAC,cAAc;gBACjB4D,IAAI,EAAC,OAAO;gBACZ9C,KAAK,EAAEmF,cAAc,CAAC7H,KAAM;gBAC5B2C,QAAQ,EAAE4C,iBAAkB;gBAC5B6B,WAAW,EAAC;cAAiB;gBAAAjI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNf,OAAA;cAAKqB,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBtB,OAAA;gBAAOkE,OAAO,EAAC,iBAAiB;gBAAA5C,QAAA,EAAC;cAAS;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAClDf,OAAA;gBACEqD,EAAE,EAAC,iBAAiB;gBACpB4D,IAAI,EAAC,UAAU;gBACf9C,KAAK,EAAEmF,cAAc,CAACtD,QAAS;gBAC/B5B,QAAQ,EAAE4C,iBAAkB;gBAAA1F,QAAA,gBAE5BtB,OAAA;kBAAQmE,KAAK,EAAC,EAAE;kBAAA7C,QAAA,EAAC;gBAAQ;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAClCf,OAAA;kBAAQmE,KAAK,EAAC,QAAQ;kBAAA7C,QAAA,EAAC;gBAAM;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACtCf,OAAA;kBAAQmE,KAAK,EAAC,UAAU;kBAAA7C,QAAA,EAAC;gBAAQ;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC1Cf,OAAA;kBAAQmE,KAAK,EAAC,gBAAgB;kBAAA7C,QAAA,EAAC;gBAAc;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACtDf,OAAA;kBAAQmE,KAAK,EAAC,iBAAiB;kBAAA7C,QAAA,EAAC;gBAAe;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACxDf,OAAA;kBAAQmE,KAAK,EAAC,UAAU;kBAAA7C,QAAA,EAAC;gBAAQ;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENf,OAAA;YAAKqB,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBtB,OAAA;cAAOkE,OAAO,EAAC,iBAAiB;cAAA5C,QAAA,EAAC;YAAQ;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACjDf,OAAA;cACE2I,IAAI,EAAC,MAAM;cACXtF,EAAE,EAAC,iBAAiB;cACpB4D,IAAI,EAAC,eAAe;cACpB9C,KAAK,EAAEmF,cAAc,CAAC/C,aAAc;cACpCnC,QAAQ,EAAE4C,iBAAkB;cAC5B6B,WAAW,EAAC;YAA6B;cAAAjI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENf,OAAA;UAAKqB,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BtB,OAAA;YACE2I,IAAI,EAAC,QAAQ;YACbtH,SAAS,EAAC,kBAAkB;YAC5BM,OAAO,EAAEa,YAAa;YACtBqC,QAAQ,EAAE6E,WAAY;YAAApI,QAAA,EAErBoI,WAAW,GAAG,YAAY,GAAG;UAAsB;YAAA9I,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CAAC,eACTf,OAAA;YACE2I,IAAI,EAAC,QAAQ;YACbtH,SAAS,EAAC,gBAAgB;YAC1BwD,QAAQ,EAAE6E,WAAY;YAAApI,QAAA,EAErBoI,WAAW,GAAG,cAAc,GAAG;UAAQ;YAAA9I,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,EAEN6I,WAAW,iBACV5J,OAAA;QAAKqB,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7BtB,OAAA;UAAAsB,QAAA,EACGkI,UAAU,CAACjF,MAAM,GAAG,CAAC,GAClB,SAASiF,UAAU,CAACjF,MAAM,QAAQiF,UAAU,CAACjF,MAAM,KAAK,CAAC,GAAG,EAAE,GAAG,GAAG,EAAE,GACtE;QAAuC;UAAA3D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEzC,CAAC,EAEJyI,UAAU,CAACjF,MAAM,GAAG,CAAC,iBACpBvE,OAAA;UAAKqB,SAAS,EAAC,WAAW;UAAAC,QAAA,EACvBkI,UAAU,CAACrG,GAAG,CAAEC,IAAI,iBACnBpD,OAAA;YAAmBqB,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACtCtB,OAAA;cAAKqB,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BtB,OAAA;gBAAAsB,QAAA,GAAK8B,IAAI,CAACoB,KAAK,EAAC,GAAC,EAACpB,IAAI,CAACqB,IAAI,IAAI,cAAc;cAAA;gBAAA7D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACnDf,OAAA;gBAAMqB,SAAS,EAAC,WAAW;gBAAAC,QAAA,EAAE8B,IAAI,CAACsB,SAAS,IAAI;cAAc;gBAAA9D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClE,CAAC,EAGL,CAACT,QAAQ,KAAK,OAAO,IAAK8C,IAAI,CAACE,aAAa,IAAIF,IAAI,CAACE,aAAa,KAAK,OAAQ,kBAC9EtD,OAAA,CAACF,mBAAmB;cAClBkD,MAAM,EAAEI,IAAI,CAACC,EAAG;cAChBsB,aAAa,EAAEvB,IAAI,CAACE,aAAa,IAAI,OAAQ;cAC7CsB,eAAe,EAAG3B,SAAS,IAAKF,wBAAwB,CAACK,IAAI,CAACC,EAAE,EAAEJ,SAAS,CAAE;cAC7E4B,QAAQ,EAAEvE,QAAQ,KAAK;YAAQ;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC,CACF,EAEAqC,IAAI,CAAC0B,UAAU,IAAI1B,IAAI,CAAC0B,UAAU,CAACP,MAAM,GAAG,CAAC,iBAC5CvE,OAAA;cAAKqB,SAAS,EAAC,aAAa;cAAAC,QAAA,EACzB8B,IAAI,CAAC0B,UAAU,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC5B,GAAG,CAAC,CAAC6B,QAAgB,EAAEC,KAAa,kBAC/DjF,OAAA;gBAEEkF,GAAG,EAAEF,QAAS;gBACdG,GAAG,EAAE,GAAG/B,IAAI,CAACoB,KAAK,IAAIpB,IAAI,CAACqB,IAAI,IAAI,MAAM,IAAIQ,KAAK,GAAG,CAAC,EAAG;gBACzD5D,SAAS,EAAC,YAAY;gBACtB+D,OAAO,EAAGf,CAAC,IAAK;kBACd;kBACCA,CAAC,CAACC,MAAM,CAAsB/C,KAAK,CAAC8D,OAAO,GAAG,MAAM;gBACvD;cAAE,GAPGJ,KAAK;gBAAArE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAQX,CACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CACN,eAEDf,OAAA;cAAKqB,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3BtB,OAAA;gBAAKqB,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBtB,OAAA;kBAAMqB,SAAS,EAAC,OAAO;kBAAAC,QAAA,EAAC;gBAAM;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACrCf,OAAA;kBAAMqB,SAAS,EAAC,OAAO;kBAAAC,QAAA,EAAE8B,IAAI,CAAC3B;gBAAK;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxC,CAAC,EAELqC,IAAI,CAAC6C,MAAM,iBACVjG,OAAA;gBAAKqB,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBtB,OAAA;kBAAMqB,SAAS,EAAC,OAAO;kBAAAC,QAAA,EAAC;gBAAO;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACtCf,OAAA;kBAAMqB,SAAS,EAAC,OAAO;kBAAAC,QAAA,GAAE8B,IAAI,CAAC6C,MAAM,EAAC,GAAC;gBAAA;kBAAArF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1C,CACN,eAEDf,OAAA;gBAAKqB,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBtB,OAAA;kBAAMqB,SAAS,EAAC,OAAO;kBAAAC,QAAA,EAAC;gBAAU;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACzCf,OAAA;kBAAMqB,SAAS,EAAC,OAAO;kBAAAC,QAAA,EAAE8B,IAAI,CAAC8C,SAAS,IAAI;gBAAS;kBAAAtF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzD,CAAC,eAENf,OAAA;gBAAKqB,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBtB,OAAA;kBAAMqB,SAAS,EAAC,OAAO;kBAAAC,QAAA,EAAC;gBAAS;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACxCf,OAAA;kBAAMqB,SAAS,EAAC,OAAO;kBAAAC,QAAA,EAAE8B,IAAI,CAACkC;gBAAc;kBAAA1E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjD,CAAC,eAENf,OAAA;gBAAKqB,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBtB,OAAA;kBAAMqB,SAAS,EAAC,OAAO;kBAAAC,QAAA,EAAC;gBAAS;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACxCf,OAAA;kBAAMqB,SAAS,EAAC,OAAO;kBAAAC,QAAA,EAAE,IAAIkC,IAAI,CAACJ,IAAI,CAACmC,UAAU,CAAC,CAACC,kBAAkB,CAAC;gBAAC;kBAAA5E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5E,CAAC,EAELqC,IAAI,CAACgF,YAAY,iBAChBpI,OAAA;gBAAKqB,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBtB,OAAA;kBAAMqB,SAAS,EAAC,OAAO;kBAAAC,QAAA,EAAC;gBAAc;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC7Cf,OAAA;kBAAMqB,SAAS,EAAC,OAAO;kBAAAC,QAAA,EAAE8B,IAAI,CAACgF;gBAAY;kBAAAxH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/C,CACN,EAEAqC,IAAI,CAACiF,YAAY,iBAChBrI,OAAA;gBAAKqB,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBtB,OAAA;kBAAMqB,SAAS,EAAC,OAAO;kBAAAC,QAAA,EAAC;gBAAa;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC5Cf,OAAA;kBAAMqB,SAAS,EAAC,OAAO;kBAAAC,QAAA,EAAE8B,IAAI,CAACiF;gBAAY;kBAAAzH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/C,CACN,EAEAqC,IAAI,CAACqD,WAAW,iBACfzG,OAAA;gBAAKqB,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBtB,OAAA;kBAAMqB,SAAS,EAAC,OAAO;kBAAAC,QAAA,EAAC;gBAAY;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC3Cf,OAAA;kBAAMqB,SAAS,EAAC,OAAO;kBAAAC,QAAA,EAAE8B,IAAI,CAACqD;gBAAW;kBAAA7F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9C,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAENf,OAAA;cAAKqB,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3BtB,OAAA;gBAAQqB,SAAS,EAAC,sBAAsB;gBAAAC,QAAA,EAAC;cAEzC;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTf,OAAA;gBAAQqB,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,EAAC;cAE3C;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA,GA1FEqC,IAAI,CAACC,EAAE;YAAAzC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OA2FZ,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAACsI,GAAA,CAhSQpI,UAAU;EAAA,QACmBrB,OAAO;AAAA;AAAAqK,GAAA,GADpChJ,UAAU;AAkSnB,SAASC,KAAKA,CAAC;EAAEP;AAAsB,CAAC,EAAE;EAAAuJ,GAAA;EACxC,MAAM;IAAEC,MAAM;IAAEC;EAAO,CAAC,GAAGxK,OAAO,CAAC,CAAC;EACpC,MAAM,CAACyK,OAAO,EAAEC,UAAU,CAAC,GAAG9K,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACsG,QAAQ,EAAEC,WAAW,CAAC,GAAGvG,QAAQ,CAAC;IACvCqC,KAAK,EAAE,EAAE;IACT0I,QAAQ,EAAE,EAAE;IACZC,QAAQ,EAAE,EAAE;IACZC,eAAe,EAAE;EACnB,CAAC,CAAC;EACF,MAAM,CAACjK,OAAO,EAAEkK,UAAU,CAAC,GAAGlL,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACmI,OAAO,EAAEgD,UAAU,CAAC,GAAGnL,QAAQ,CAAC,EAAE,CAAC;EAE1C,MAAMwH,iBAAiB,GAAI3C,CAAsC,IAAK;IACpE,MAAM;MAAE4C,IAAI;MAAE9C;IAAM,CAAC,GAAGE,CAAC,CAACC,MAAM;IAChCyB,WAAW,CAAC7C,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAAC+D,IAAI,GAAG9C;IACV,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAM+C,YAAY,GAAG,MAAO7C,CAAkB,IAAK;IACjDA,CAAC,CAAC8C,cAAc,CAAC,CAAC;IAClBuD,UAAU,CAAC,IAAI,CAAC;IAChBC,UAAU,CAAC,EAAE,CAAC;IAEd,IAAI;MACF,IAAIN,OAAO,EAAE;QACX,MAAM;UAAE3H;QAAM,CAAC,GAAG,MAAMyH,MAAM,CAACrE,QAAQ,CAACjE,KAAK,EAAEiE,QAAQ,CAACyE,QAAQ,CAAC;QACjE,IAAI7H,KAAK,EAAE;UACTiI,UAAU,CAACjI,KAAK,CAACiF,OAAO,CAAC;QAC3B,CAAC,MAAM;UACLgD,UAAU,CAAC,yBAAyB,CAAC;UACrCrD,UAAU,CAAC,MAAM3G,UAAU,CAAC,MAAM,CAAC,EAAE,IAAI,CAAC;QAC5C;MACF,CAAC,MAAM;QACL,IAAImF,QAAQ,CAACyE,QAAQ,KAAKzE,QAAQ,CAAC2E,eAAe,EAAE;UAClDE,UAAU,CAAC,wBAAwB,CAAC;UACpCD,UAAU,CAAC,KAAK,CAAC;UACjB;QACF;QACA,IAAI5E,QAAQ,CAACyE,QAAQ,CAAChG,MAAM,GAAG,CAAC,EAAE;UAChCoG,UAAU,CAAC,wCAAwC,CAAC;UACpDD,UAAU,CAAC,KAAK,CAAC;UACjB;QACF;QAEA,MAAM;UAAEhI;QAAM,CAAC,GAAG,MAAM0H,MAAM,CAACtE,QAAQ,CAACjE,KAAK,EAAEiE,QAAQ,CAACyE,QAAQ,EAAEzE,QAAQ,CAAC0E,QAAQ,CAAC;QACpF,IAAI9H,KAAK,EAAE;UACTiI,UAAU,CAACjI,KAAK,CAACiF,OAAO,CAAC;QAC3B,CAAC,MAAM;UACLgD,UAAU,CAAC,kEAAkE,CAAC;QAChF;MACF;IACF,CAAC,CAAC,OAAOjI,KAAK,EAAE;MACdiI,UAAU,CAAC,8BAA8B,CAAC;IAC5C,CAAC,SAAS;MACRD,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,oBACE1K,OAAA;IAAKqB,SAAS,EAAC,gBAAgB;IAAAC,QAAA,gBAC7BtB,OAAA;MAAKqB,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1BtB,OAAA;QAAQqB,SAAS,EAAC,aAAa;QAACM,OAAO,EAAEA,CAAA,KAAMhB,UAAU,CAAC,MAAM,CAAE;QAAAW,QAAA,EAAC;MAEnE;QAAAV,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTf,OAAA;QAAAsB,QAAA,EAAK+I,OAAO,GAAG,SAAS,GAAG;MAAgB;QAAAzJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACjDf,OAAA;QAAAsB,QAAA,EAAI+I,OAAO,GAAG,yBAAyB,GAAG;MAAkD;QAAAzJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9F,CAAC,eAENf,OAAA;MAAKqB,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC7BtB,OAAA;QAAKqB,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBtB,OAAA;UACEqB,SAAS,EAAE,YAAYgJ,OAAO,GAAG,QAAQ,GAAG,EAAE,EAAG;UACjD1I,OAAO,EAAEA,CAAA,KAAM2I,UAAU,CAAC,IAAI,CAAE;UAAAhJ,QAAA,EACjC;QAED;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTf,OAAA;UACEqB,SAAS,EAAE,YAAY,CAACgJ,OAAO,GAAG,QAAQ,GAAG,EAAE,EAAG;UAClD1I,OAAO,EAAEA,CAAA,KAAM2I,UAAU,CAAC,KAAK,CAAE;UAAAhJ,QAAA,EAClC;QAED;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,EAEL4G,OAAO,iBACN3H,OAAA;QAAKqB,SAAS,EAAE,kBAAkBsG,OAAO,CAACa,QAAQ,CAAC,OAAO,CAAC,IAAIb,OAAO,CAACa,QAAQ,CAAC,OAAO,CAAC,GAAG,OAAO,GAAG,SAAS,EAAG;QAAAlH,QAAA,EAC9GqG;MAAO;QAAA/G,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CACN,eAEDf,OAAA;QAAMqB,SAAS,EAAC,WAAW;QAACoH,QAAQ,EAAEvB,YAAa;QAAA5F,QAAA,GAChD,CAAC+I,OAAO,iBACPrK,OAAA;UAAKqB,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBtB,OAAA;YAAOkE,OAAO,EAAC,UAAU;YAAA5C,QAAA,EAAC;UAAS;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC3Cf,OAAA;YACE2I,IAAI,EAAC,MAAM;YACXtF,EAAE,EAAC,UAAU;YACb4D,IAAI,EAAC,UAAU;YACf9C,KAAK,EAAE2B,QAAQ,CAAC0E,QAAS;YACzBpG,QAAQ,EAAE4C,iBAAkB;YAC5B4B,QAAQ,EAAE,CAACyB,OAAQ;YACnBxB,WAAW,EAAC;UAAgB;YAAAjI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN,eAEDf,OAAA;UAAKqB,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBtB,OAAA;YAAOkE,OAAO,EAAC,OAAO;YAAA5C,QAAA,EAAC;UAAK;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACpCf,OAAA;YACE2I,IAAI,EAAC,OAAO;YACZtF,EAAE,EAAC,OAAO;YACV4D,IAAI,EAAC,OAAO;YACZ9C,KAAK,EAAE2B,QAAQ,CAACjE,KAAM;YACtBuC,QAAQ,EAAE4C,iBAAkB;YAC5B4B,QAAQ;YACRC,WAAW,EAAC;UAAgB;YAAAjI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENf,OAAA;UAAKqB,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBtB,OAAA;YAAOkE,OAAO,EAAC,UAAU;YAAA5C,QAAA,EAAC;UAAQ;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC1Cf,OAAA;YACE2I,IAAI,EAAC,UAAU;YACftF,EAAE,EAAC,UAAU;YACb4D,IAAI,EAAC,UAAU;YACf9C,KAAK,EAAE2B,QAAQ,CAACyE,QAAS;YACzBnG,QAAQ,EAAE4C,iBAAkB;YAC5B4B,QAAQ;YACRC,WAAW,EAAEwB,OAAO,GAAG,eAAe,GAAG;UAAwB;YAAAzJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,EAEL,CAACsJ,OAAO,iBACPrK,OAAA;UAAKqB,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBtB,OAAA;YAAOkE,OAAO,EAAC,iBAAiB;YAAA5C,QAAA,EAAC;UAAgB;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACzDf,OAAA;YACE2I,IAAI,EAAC,UAAU;YACftF,EAAE,EAAC,iBAAiB;YACpB4D,IAAI,EAAC,iBAAiB;YACtB9C,KAAK,EAAE2B,QAAQ,CAAC2E,eAAgB;YAChCrG,QAAQ,EAAE4C,iBAAkB;YAC5B4B,QAAQ,EAAE,CAACyB,OAAQ;YACnBxB,WAAW,EAAC;UAAuB;YAAAjI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN,eAEDf,OAAA;UACE2I,IAAI,EAAC,QAAQ;UACbtH,SAAS,EAAC,2BAA2B;UACrCwD,QAAQ,EAAErE,OAAQ;UAAAc,QAAA,EAEjBd,OAAO,GAAG,gBAAgB,GAAI6J,OAAO,GAAG,SAAS,GAAG;QAAiB;UAAAzJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAACmJ,GAAA,CAhKQhJ,KAAK;EAAA,QACetB,OAAO;AAAA;AAAAgL,GAAA,GAD3B1J,KAAK;AAkKd,eAAea,GAAG;AAAC,IAAAD,EAAA,EAAAE,GAAA,EAAA0D,GAAA,EAAAC,GAAA,EAAAyD,GAAA,EAAAa,GAAA,EAAAW,GAAA;AAAAC,YAAA,CAAA/I,EAAA;AAAA+I,YAAA,CAAA7I,GAAA;AAAA6I,YAAA,CAAAnF,GAAA;AAAAmF,YAAA,CAAAlF,GAAA;AAAAkF,YAAA,CAAAzB,GAAA;AAAAyB,YAAA,CAAAZ,GAAA;AAAAY,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}