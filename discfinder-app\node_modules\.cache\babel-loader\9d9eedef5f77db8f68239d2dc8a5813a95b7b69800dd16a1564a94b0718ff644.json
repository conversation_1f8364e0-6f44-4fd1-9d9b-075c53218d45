{"ast": null, "code": "import { createClient } from '@supabase/supabase-js';\n\n// Supabase configuration\nconst supabaseUrl = process.env.REACT_APP_SUPABASE_URL || 'https://demo.supabase.co';\nconst supabaseAnonKey = process.env.REACT_APP_SUPABASE_ANON_KEY || 'demo-key';\nexport const supabase = createClient(supabaseUrl, supabaseAnonKey);\n\n// Database types\n\n// Helper functions for database operations\nexport const discService = {\n  // Ensure demo user exists\n  async ensureDemoUser() {\n    const demoUserId = '00000000-0000-0000-0000-000000000000';\n    try {\n      // Check if demo user exists\n      const {\n        data: existingUser\n      } = await supabase.from('profiles').select('id').eq('id', demoUserId).single();\n      if (!existingUser) {\n        // Create demo user\n        const {\n          error\n        } = await supabase.from('profiles').insert([{\n          id: demoUserId,\n          email: '<EMAIL>',\n          full_name: 'Demo User'\n        }]);\n        if (error) {\n          console.warn('Could not create demo user:', error);\n        }\n      }\n      return demoUserId;\n    } catch (error) {\n      console.warn('Demo user setup failed:', error);\n      return demoUserId;\n    }\n  },\n  // Create a new found disc report\n  async createFoundDisc(discData) {\n    try {\n      // Ensure demo user exists first\n      await this.ensureDemoUser();\n      const {\n        data,\n        error\n      } = await supabase.from('found_discs').insert([{\n        ...discData,\n        status: 'active'\n      }]).select().single();\n      if (error) throw error;\n      return {\n        data,\n        error: null\n      };\n    } catch (error) {\n      console.error('Error creating found disc:', error);\n      return {\n        data: null,\n        error\n      };\n    }\n  },\n  // Get all active found discs\n  async getFoundDiscs() {\n    try {\n      const {\n        data,\n        error\n      } = await supabase.from('found_discs').select('*').eq('status', 'active').order('created_at', {\n        ascending: false\n      });\n      if (error) throw error;\n      return {\n        data,\n        error: null\n      };\n    } catch (error) {\n      console.error('Error fetching found discs:', error);\n      return {\n        data: null,\n        error\n      };\n    }\n  },\n  // Search found discs by criteria\n  async searchFoundDiscs(searchCriteria) {\n    try {\n      let query = supabase.from('found_discs').select('*').eq('status', 'active');\n      if (searchCriteria.brand) {\n        query = query.ilike('brand', `%${searchCriteria.brand}%`);\n      }\n      if (searchCriteria.model) {\n        query = query.ilike('model', `%${searchCriteria.model}%`);\n      }\n      if (searchCriteria.color) {\n        query = query.ilike('color', `%${searchCriteria.color}%`);\n      }\n      if (searchCriteria.discType) {\n        query = query.eq('disc_type', searchCriteria.discType);\n      }\n      if (searchCriteria.locationFound) {\n        query = query.ilike('location_found', `%${searchCriteria.locationFound}%`);\n      }\n      const {\n        data,\n        error\n      } = await query.order('created_at', {\n        ascending: false\n      });\n      if (error) throw error;\n      return {\n        data,\n        error: null\n      };\n    } catch (error) {\n      console.error('Error searching found discs:', error);\n      return {\n        data: null,\n        error\n      };\n    }\n  },\n  // Test connection to Supabase\n  async testConnection() {\n    try {\n      const {\n        data,\n        error\n      } = await supabase.from('found_discs').select('count').limit(1);\n      return {\n        connected: !error,\n        error\n      };\n    } catch (error) {\n      return {\n        connected: false,\n        error\n      };\n    }\n  }\n};", "map": {"version": 3, "names": ["createClient", "supabaseUrl", "process", "env", "REACT_APP_SUPABASE_URL", "supabaseAnonKey", "REACT_APP_SUPABASE_ANON_KEY", "supabase", "discService", "ensureDemoUser", "demoUserId", "data", "existingUser", "from", "select", "eq", "single", "error", "insert", "id", "email", "full_name", "console", "warn", "createFoundDisc", "discData", "status", "getFoundDiscs", "order", "ascending", "searchFoundDiscs", "searchCriteria", "query", "brand", "ilike", "model", "color", "discType", "locationFound", "testConnection", "limit", "connected"], "sources": ["C:/Users/<USER>/lostandfound/discfinder-app/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js'\n\n// Supabase configuration\nconst supabaseUrl = process.env.REACT_APP_SUPABASE_URL || 'https://demo.supabase.co'\nconst supabaseAnonKey = process.env.REACT_APP_SUPABASE_ANON_KEY || 'demo-key'\n\nexport const supabase = createClient(supabaseUrl, supabaseAnonKey)\n\n// Database types\nexport interface Profile {\n  id: string\n  email: string\n  full_name?: string\n  phone?: string\n  created_at: string\n  updated_at: string\n}\n\nexport interface FoundDisc {\n  id: string\n  finder_id: string\n  brand: string\n  model?: string\n  disc_type?: 'driver' | 'fairway_driver' | 'midrange' | 'putter' | 'approach' | 'distance_driver'\n  color: string\n  weight?: number\n  condition?: 'new' | 'excellent' | 'good' | 'fair' | 'poor'\n  plastic_type?: string\n  stamp_text?: string\n  phone_number?: string\n  name_on_disc?: string\n  location_found: string\n  location_coordinates?: { x: number; y: number }\n  found_date: string\n  description?: string\n  image_urls?: string[]\n  status: 'active' | 'claimed' | 'expired' | 'spam'\n  created_at: string\n  updated_at: string\n}\n\nexport interface LostDisc {\n  id: string\n  owner_id: string\n  brand: string\n  model?: string\n  disc_type?: 'driver' | 'fairway_driver' | 'midrange' | 'putter' | 'approach' | 'distance_driver'\n  color: string\n  weight?: number\n  plastic_type?: string\n  stamp_text?: string\n  location_lost: string\n  location_coordinates?: { x: number; y: number }\n  lost_date: string\n  description?: string\n  reward_offered?: number\n  contact_preference: string\n  status: 'active' | 'claimed' | 'expired' | 'spam'\n  created_at: string\n  updated_at: string\n}\n\nexport interface DiscMatch {\n  id: string\n  found_disc_id: string\n  lost_disc_id: string\n  match_score: number\n  status: 'potential' | 'confirmed' | 'rejected'\n  finder_contacted_at?: string\n  owner_contacted_at?: string\n  created_at: string\n  updated_at: string\n  found_disc?: FoundDisc\n  lost_disc?: LostDisc\n}\n\n// Helper functions for database operations\nexport const discService = {\n  // Ensure demo user exists\n  async ensureDemoUser() {\n    const demoUserId = '00000000-0000-0000-0000-000000000000';\n    try {\n      // Check if demo user exists\n      const { data: existingUser } = await supabase\n        .from('profiles')\n        .select('id')\n        .eq('id', demoUserId)\n        .single();\n\n      if (!existingUser) {\n        // Create demo user\n        const { error } = await supabase\n          .from('profiles')\n          .insert([{\n            id: demoUserId,\n            email: '<EMAIL>',\n            full_name: 'Demo User'\n          }]);\n\n        if (error) {\n          console.warn('Could not create demo user:', error);\n        }\n      }\n      return demoUserId;\n    } catch (error) {\n      console.warn('Demo user setup failed:', error);\n      return demoUserId;\n    }\n  },\n\n  // Create a new found disc report\n  async createFoundDisc(discData: Omit<FoundDisc, 'id' | 'created_at' | 'updated_at' | 'status'>) {\n    try {\n      // Ensure demo user exists first\n      await this.ensureDemoUser();\n\n      const { data, error } = await supabase\n        .from('found_discs')\n        .insert([{\n          ...discData,\n          status: 'active'\n        }])\n        .select()\n        .single()\n\n      if (error) throw error\n      return { data, error: null }\n    } catch (error) {\n      console.error('Error creating found disc:', error)\n      return { data: null, error }\n    }\n  },\n\n  // Get all active found discs\n  async getFoundDiscs() {\n    try {\n      const { data, error } = await supabase\n        .from('found_discs')\n        .select('*')\n        .eq('status', 'active')\n        .order('created_at', { ascending: false })\n\n      if (error) throw error\n      return { data, error: null }\n    } catch (error) {\n      console.error('Error fetching found discs:', error)\n      return { data: null, error }\n    }\n  },\n\n  // Search found discs by criteria\n  async searchFoundDiscs(searchCriteria: {\n    brand?: string\n    model?: string\n    color?: string\n    discType?: string\n    locationFound?: string\n  }) {\n    try {\n      let query = supabase\n        .from('found_discs')\n        .select('*')\n        .eq('status', 'active')\n\n      if (searchCriteria.brand) {\n        query = query.ilike('brand', `%${searchCriteria.brand}%`)\n      }\n      if (searchCriteria.model) {\n        query = query.ilike('model', `%${searchCriteria.model}%`)\n      }\n      if (searchCriteria.color) {\n        query = query.ilike('color', `%${searchCriteria.color}%`)\n      }\n      if (searchCriteria.discType) {\n        query = query.eq('disc_type', searchCriteria.discType)\n      }\n      if (searchCriteria.locationFound) {\n        query = query.ilike('location_found', `%${searchCriteria.locationFound}%`)\n      }\n\n      const { data, error } = await query.order('created_at', { ascending: false })\n\n      if (error) throw error\n      return { data, error: null }\n    } catch (error) {\n      console.error('Error searching found discs:', error)\n      return { data: null, error }\n    }\n  },\n\n  // Test connection to Supabase\n  async testConnection() {\n    try {\n      const { data, error } = await supabase\n        .from('found_discs')\n        .select('count')\n        .limit(1)\n\n      return { connected: !error, error }\n    } catch (error) {\n      return { connected: false, error }\n    }\n  }\n}\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,uBAAuB;;AAEpD;AACA,MAAMC,WAAW,GAAGC,OAAO,CAACC,GAAG,CAACC,sBAAsB,IAAI,0BAA0B;AACpF,MAAMC,eAAe,GAAGH,OAAO,CAACC,GAAG,CAACG,2BAA2B,IAAI,UAAU;AAE7E,OAAO,MAAMC,QAAQ,GAAGP,YAAY,CAACC,WAAW,EAAEI,eAAe,CAAC;;AAElE;;AAoEA;AACA,OAAO,MAAMG,WAAW,GAAG;EACzB;EACA,MAAMC,cAAcA,CAAA,EAAG;IACrB,MAAMC,UAAU,GAAG,sCAAsC;IACzD,IAAI;MACF;MACA,MAAM;QAAEC,IAAI,EAAEC;MAAa,CAAC,GAAG,MAAML,QAAQ,CAC1CM,IAAI,CAAC,UAAU,CAAC,CAChBC,MAAM,CAAC,IAAI,CAAC,CACZC,EAAE,CAAC,IAAI,EAAEL,UAAU,CAAC,CACpBM,MAAM,CAAC,CAAC;MAEX,IAAI,CAACJ,YAAY,EAAE;QACjB;QACA,MAAM;UAAEK;QAAM,CAAC,GAAG,MAAMV,QAAQ,CAC7BM,IAAI,CAAC,UAAU,CAAC,CAChBK,MAAM,CAAC,CAAC;UACPC,EAAE,EAAET,UAAU;UACdU,KAAK,EAAE,qBAAqB;UAC5BC,SAAS,EAAE;QACb,CAAC,CAAC,CAAC;QAEL,IAAIJ,KAAK,EAAE;UACTK,OAAO,CAACC,IAAI,CAAC,6BAA6B,EAAEN,KAAK,CAAC;QACpD;MACF;MACA,OAAOP,UAAU;IACnB,CAAC,CAAC,OAAOO,KAAK,EAAE;MACdK,OAAO,CAACC,IAAI,CAAC,yBAAyB,EAAEN,KAAK,CAAC;MAC9C,OAAOP,UAAU;IACnB;EACF,CAAC;EAED;EACA,MAAMc,eAAeA,CAACC,QAAwE,EAAE;IAC9F,IAAI;MACF;MACA,MAAM,IAAI,CAAChB,cAAc,CAAC,CAAC;MAE3B,MAAM;QAAEE,IAAI;QAAEM;MAAM,CAAC,GAAG,MAAMV,QAAQ,CACnCM,IAAI,CAAC,aAAa,CAAC,CACnBK,MAAM,CAAC,CAAC;QACP,GAAGO,QAAQ;QACXC,MAAM,EAAE;MACV,CAAC,CAAC,CAAC,CACFZ,MAAM,CAAC,CAAC,CACRE,MAAM,CAAC,CAAC;MAEX,IAAIC,KAAK,EAAE,MAAMA,KAAK;MACtB,OAAO;QAAEN,IAAI;QAAEM,KAAK,EAAE;MAAK,CAAC;IAC9B,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdK,OAAO,CAACL,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClD,OAAO;QAAEN,IAAI,EAAE,IAAI;QAAEM;MAAM,CAAC;IAC9B;EACF,CAAC;EAED;EACA,MAAMU,aAAaA,CAAA,EAAG;IACpB,IAAI;MACF,MAAM;QAAEhB,IAAI;QAAEM;MAAM,CAAC,GAAG,MAAMV,QAAQ,CACnCM,IAAI,CAAC,aAAa,CAAC,CACnBC,MAAM,CAAC,GAAG,CAAC,CACXC,EAAE,CAAC,QAAQ,EAAE,QAAQ,CAAC,CACtBa,KAAK,CAAC,YAAY,EAAE;QAAEC,SAAS,EAAE;MAAM,CAAC,CAAC;MAE5C,IAAIZ,KAAK,EAAE,MAAMA,KAAK;MACtB,OAAO;QAAEN,IAAI;QAAEM,KAAK,EAAE;MAAK,CAAC;IAC9B,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdK,OAAO,CAACL,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnD,OAAO;QAAEN,IAAI,EAAE,IAAI;QAAEM;MAAM,CAAC;IAC9B;EACF,CAAC;EAED;EACA,MAAMa,gBAAgBA,CAACC,cAMtB,EAAE;IACD,IAAI;MACF,IAAIC,KAAK,GAAGzB,QAAQ,CACjBM,IAAI,CAAC,aAAa,CAAC,CACnBC,MAAM,CAAC,GAAG,CAAC,CACXC,EAAE,CAAC,QAAQ,EAAE,QAAQ,CAAC;MAEzB,IAAIgB,cAAc,CAACE,KAAK,EAAE;QACxBD,KAAK,GAAGA,KAAK,CAACE,KAAK,CAAC,OAAO,EAAE,IAAIH,cAAc,CAACE,KAAK,GAAG,CAAC;MAC3D;MACA,IAAIF,cAAc,CAACI,KAAK,EAAE;QACxBH,KAAK,GAAGA,KAAK,CAACE,KAAK,CAAC,OAAO,EAAE,IAAIH,cAAc,CAACI,KAAK,GAAG,CAAC;MAC3D;MACA,IAAIJ,cAAc,CAACK,KAAK,EAAE;QACxBJ,KAAK,GAAGA,KAAK,CAACE,KAAK,CAAC,OAAO,EAAE,IAAIH,cAAc,CAACK,KAAK,GAAG,CAAC;MAC3D;MACA,IAAIL,cAAc,CAACM,QAAQ,EAAE;QAC3BL,KAAK,GAAGA,KAAK,CAACjB,EAAE,CAAC,WAAW,EAAEgB,cAAc,CAACM,QAAQ,CAAC;MACxD;MACA,IAAIN,cAAc,CAACO,aAAa,EAAE;QAChCN,KAAK,GAAGA,KAAK,CAACE,KAAK,CAAC,gBAAgB,EAAE,IAAIH,cAAc,CAACO,aAAa,GAAG,CAAC;MAC5E;MAEA,MAAM;QAAE3B,IAAI;QAAEM;MAAM,CAAC,GAAG,MAAMe,KAAK,CAACJ,KAAK,CAAC,YAAY,EAAE;QAAEC,SAAS,EAAE;MAAM,CAAC,CAAC;MAE7E,IAAIZ,KAAK,EAAE,MAAMA,KAAK;MACtB,OAAO;QAAEN,IAAI;QAAEM,KAAK,EAAE;MAAK,CAAC;IAC9B,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdK,OAAO,CAACL,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpD,OAAO;QAAEN,IAAI,EAAE,IAAI;QAAEM;MAAM,CAAC;IAC9B;EACF,CAAC;EAED;EACA,MAAMsB,cAAcA,CAAA,EAAG;IACrB,IAAI;MACF,MAAM;QAAE5B,IAAI;QAAEM;MAAM,CAAC,GAAG,MAAMV,QAAQ,CACnCM,IAAI,CAAC,aAAa,CAAC,CACnBC,MAAM,CAAC,OAAO,CAAC,CACf0B,KAAK,CAAC,CAAC,CAAC;MAEX,OAAO;QAAEC,SAAS,EAAE,CAACxB,KAAK;QAAEA;MAAM,CAAC;IACrC,CAAC,CAAC,OAAOA,KAAK,EAAE;MACd,OAAO;QAAEwB,SAAS,EAAE,KAAK;QAAExB;MAAM,CAAC;IACpC;EACF;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}