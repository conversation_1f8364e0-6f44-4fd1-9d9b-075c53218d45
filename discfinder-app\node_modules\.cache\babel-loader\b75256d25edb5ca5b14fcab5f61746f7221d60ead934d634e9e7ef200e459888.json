{"ast": null, "code": "\"use strict\";\n\nvar __importDefault = this && this.__importDefault || function (mod) {\n  return mod && mod.__esModule ? mod : {\n    \"default\": mod\n  };\n};\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nconst PostgrestTransformBuilder_1 = __importDefault(require(\"./PostgrestTransformBuilder\"));\nclass PostgrestFilterBuilder extends PostgrestTransformBuilder_1.default {\n  /**\n   * Match only rows where `column` is equal to `value`.\n   *\n   * To check if the value of `column` is NULL, you should use `.is()` instead.\n   *\n   * @param column - The column to filter on\n   * @param value - The value to filter with\n   */\n  eq(column, value) {\n    this.url.searchParams.append(column, `eq.${value}`);\n    return this;\n  }\n  /**\n   * Match only rows where `column` is not equal to `value`.\n   *\n   * @param column - The column to filter on\n   * @param value - The value to filter with\n   */\n  neq(column, value) {\n    this.url.searchParams.append(column, `neq.${value}`);\n    return this;\n  }\n  /**\n   * Match only rows where `column` is greater than `value`.\n   *\n   * @param column - The column to filter on\n   * @param value - The value to filter with\n   */\n  gt(column, value) {\n    this.url.searchParams.append(column, `gt.${value}`);\n    return this;\n  }\n  /**\n   * Match only rows where `column` is greater than or equal to `value`.\n   *\n   * @param column - The column to filter on\n   * @param value - The value to filter with\n   */\n  gte(column, value) {\n    this.url.searchParams.append(column, `gte.${value}`);\n    return this;\n  }\n  /**\n   * Match only rows where `column` is less than `value`.\n   *\n   * @param column - The column to filter on\n   * @param value - The value to filter with\n   */\n  lt(column, value) {\n    this.url.searchParams.append(column, `lt.${value}`);\n    return this;\n  }\n  /**\n   * Match only rows where `column` is less than or equal to `value`.\n   *\n   * @param column - The column to filter on\n   * @param value - The value to filter with\n   */\n  lte(column, value) {\n    this.url.searchParams.append(column, `lte.${value}`);\n    return this;\n  }\n  /**\n   * Match only rows where `column` matches `pattern` case-sensitively.\n   *\n   * @param column - The column to filter on\n   * @param pattern - The pattern to match with\n   */\n  like(column, pattern) {\n    this.url.searchParams.append(column, `like.${pattern}`);\n    return this;\n  }\n  /**\n   * Match only rows where `column` matches all of `patterns` case-sensitively.\n   *\n   * @param column - The column to filter on\n   * @param patterns - The patterns to match with\n   */\n  likeAllOf(column, patterns) {\n    this.url.searchParams.append(column, `like(all).{${patterns.join(',')}}`);\n    return this;\n  }\n  /**\n   * Match only rows where `column` matches any of `patterns` case-sensitively.\n   *\n   * @param column - The column to filter on\n   * @param patterns - The patterns to match with\n   */\n  likeAnyOf(column, patterns) {\n    this.url.searchParams.append(column, `like(any).{${patterns.join(',')}}`);\n    return this;\n  }\n  /**\n   * Match only rows where `column` matches `pattern` case-insensitively.\n   *\n   * @param column - The column to filter on\n   * @param pattern - The pattern to match with\n   */\n  ilike(column, pattern) {\n    this.url.searchParams.append(column, `ilike.${pattern}`);\n    return this;\n  }\n  /**\n   * Match only rows where `column` matches all of `patterns` case-insensitively.\n   *\n   * @param column - The column to filter on\n   * @param patterns - The patterns to match with\n   */\n  ilikeAllOf(column, patterns) {\n    this.url.searchParams.append(column, `ilike(all).{${patterns.join(',')}}`);\n    return this;\n  }\n  /**\n   * Match only rows where `column` matches any of `patterns` case-insensitively.\n   *\n   * @param column - The column to filter on\n   * @param patterns - The patterns to match with\n   */\n  ilikeAnyOf(column, patterns) {\n    this.url.searchParams.append(column, `ilike(any).{${patterns.join(',')}}`);\n    return this;\n  }\n  /**\n   * Match only rows where `column` IS `value`.\n   *\n   * For non-boolean columns, this is only relevant for checking if the value of\n   * `column` is NULL by setting `value` to `null`.\n   *\n   * For boolean columns, you can also set `value` to `true` or `false` and it\n   * will behave the same way as `.eq()`.\n   *\n   * @param column - The column to filter on\n   * @param value - The value to filter with\n   */\n  is(column, value) {\n    this.url.searchParams.append(column, `is.${value}`);\n    return this;\n  }\n  /**\n   * Match only rows where `column` is included in the `values` array.\n   *\n   * @param column - The column to filter on\n   * @param values - The values array to filter with\n   */\n  in(column, values) {\n    const cleanedValues = Array.from(new Set(values)).map(s => {\n      // handle postgrest reserved characters\n      // https://postgrest.org/en/v7.0.0/api.html#reserved-characters\n      if (typeof s === 'string' && new RegExp('[,()]').test(s)) return `\"${s}\"`;else return `${s}`;\n    }).join(',');\n    this.url.searchParams.append(column, `in.(${cleanedValues})`);\n    return this;\n  }\n  /**\n   * Only relevant for jsonb, array, and range columns. Match only rows where\n   * `column` contains every element appearing in `value`.\n   *\n   * @param column - The jsonb, array, or range column to filter on\n   * @param value - The jsonb, array, or range value to filter with\n   */\n  contains(column, value) {\n    if (typeof value === 'string') {\n      // range types can be inclusive '[', ']' or exclusive '(', ')' so just\n      // keep it simple and accept a string\n      this.url.searchParams.append(column, `cs.${value}`);\n    } else if (Array.isArray(value)) {\n      // array\n      this.url.searchParams.append(column, `cs.{${value.join(',')}}`);\n    } else {\n      // json\n      this.url.searchParams.append(column, `cs.${JSON.stringify(value)}`);\n    }\n    return this;\n  }\n  /**\n   * Only relevant for jsonb, array, and range columns. Match only rows where\n   * every element appearing in `column` is contained by `value`.\n   *\n   * @param column - The jsonb, array, or range column to filter on\n   * @param value - The jsonb, array, or range value to filter with\n   */\n  containedBy(column, value) {\n    if (typeof value === 'string') {\n      // range\n      this.url.searchParams.append(column, `cd.${value}`);\n    } else if (Array.isArray(value)) {\n      // array\n      this.url.searchParams.append(column, `cd.{${value.join(',')}}`);\n    } else {\n      // json\n      this.url.searchParams.append(column, `cd.${JSON.stringify(value)}`);\n    }\n    return this;\n  }\n  /**\n   * Only relevant for range columns. Match only rows where every element in\n   * `column` is greater than any element in `range`.\n   *\n   * @param column - The range column to filter on\n   * @param range - The range to filter with\n   */\n  rangeGt(column, range) {\n    this.url.searchParams.append(column, `sr.${range}`);\n    return this;\n  }\n  /**\n   * Only relevant for range columns. Match only rows where every element in\n   * `column` is either contained in `range` or greater than any element in\n   * `range`.\n   *\n   * @param column - The range column to filter on\n   * @param range - The range to filter with\n   */\n  rangeGte(column, range) {\n    this.url.searchParams.append(column, `nxl.${range}`);\n    return this;\n  }\n  /**\n   * Only relevant for range columns. Match only rows where every element in\n   * `column` is less than any element in `range`.\n   *\n   * @param column - The range column to filter on\n   * @param range - The range to filter with\n   */\n  rangeLt(column, range) {\n    this.url.searchParams.append(column, `sl.${range}`);\n    return this;\n  }\n  /**\n   * Only relevant for range columns. Match only rows where every element in\n   * `column` is either contained in `range` or less than any element in\n   * `range`.\n   *\n   * @param column - The range column to filter on\n   * @param range - The range to filter with\n   */\n  rangeLte(column, range) {\n    this.url.searchParams.append(column, `nxr.${range}`);\n    return this;\n  }\n  /**\n   * Only relevant for range columns. Match only rows where `column` is\n   * mutually exclusive to `range` and there can be no element between the two\n   * ranges.\n   *\n   * @param column - The range column to filter on\n   * @param range - The range to filter with\n   */\n  rangeAdjacent(column, range) {\n    this.url.searchParams.append(column, `adj.${range}`);\n    return this;\n  }\n  /**\n   * Only relevant for array and range columns. Match only rows where\n   * `column` and `value` have an element in common.\n   *\n   * @param column - The array or range column to filter on\n   * @param value - The array or range value to filter with\n   */\n  overlaps(column, value) {\n    if (typeof value === 'string') {\n      // range\n      this.url.searchParams.append(column, `ov.${value}`);\n    } else {\n      // array\n      this.url.searchParams.append(column, `ov.{${value.join(',')}}`);\n    }\n    return this;\n  }\n  /**\n   * Only relevant for text and tsvector columns. Match only rows where\n   * `column` matches the query string in `query`.\n   *\n   * @param column - The text or tsvector column to filter on\n   * @param query - The query text to match with\n   * @param options - Named parameters\n   * @param options.config - The text search configuration to use\n   * @param options.type - Change how the `query` text is interpreted\n   */\n  textSearch(column, query, {\n    config,\n    type\n  } = {}) {\n    let typePart = '';\n    if (type === 'plain') {\n      typePart = 'pl';\n    } else if (type === 'phrase') {\n      typePart = 'ph';\n    } else if (type === 'websearch') {\n      typePart = 'w';\n    }\n    const configPart = config === undefined ? '' : `(${config})`;\n    this.url.searchParams.append(column, `${typePart}fts${configPart}.${query}`);\n    return this;\n  }\n  /**\n   * Match only rows where each column in `query` keys is equal to its\n   * associated value. Shorthand for multiple `.eq()`s.\n   *\n   * @param query - The object to filter with, with column names as keys mapped\n   * to their filter values\n   */\n  match(query) {\n    Object.entries(query).forEach(([column, value]) => {\n      this.url.searchParams.append(column, `eq.${value}`);\n    });\n    return this;\n  }\n  /**\n   * Match only rows which doesn't satisfy the filter.\n   *\n   * Unlike most filters, `opearator` and `value` are used as-is and need to\n   * follow [PostgREST\n   * syntax](https://postgrest.org/en/stable/api.html#operators). You also need\n   * to make sure they are properly sanitized.\n   *\n   * @param column - The column to filter on\n   * @param operator - The operator to be negated to filter with, following\n   * PostgREST syntax\n   * @param value - The value to filter with, following PostgREST syntax\n   */\n  not(column, operator, value) {\n    this.url.searchParams.append(column, `not.${operator}.${value}`);\n    return this;\n  }\n  /**\n   * Match only rows which satisfy at least one of the filters.\n   *\n   * Unlike most filters, `filters` is used as-is and needs to follow [PostgREST\n   * syntax](https://postgrest.org/en/stable/api.html#operators). You also need\n   * to make sure it's properly sanitized.\n   *\n   * It's currently not possible to do an `.or()` filter across multiple tables.\n   *\n   * @param filters - The filters to use, following PostgREST syntax\n   * @param options - Named parameters\n   * @param options.referencedTable - Set this to filter on referenced tables\n   * instead of the parent table\n   * @param options.foreignTable - Deprecated, use `referencedTable` instead\n   */\n  or(filters, {\n    foreignTable,\n    referencedTable = foreignTable\n  } = {}) {\n    const key = referencedTable ? `${referencedTable}.or` : 'or';\n    this.url.searchParams.append(key, `(${filters})`);\n    return this;\n  }\n  /**\n   * Match only rows which satisfy the filter. This is an escape hatch - you\n   * should use the specific filter methods wherever possible.\n   *\n   * Unlike most filters, `opearator` and `value` are used as-is and need to\n   * follow [PostgREST\n   * syntax](https://postgrest.org/en/stable/api.html#operators). You also need\n   * to make sure they are properly sanitized.\n   *\n   * @param column - The column to filter on\n   * @param operator - The operator to filter with, following PostgREST syntax\n   * @param value - The value to filter with, following PostgREST syntax\n   */\n  filter(column, operator, value) {\n    this.url.searchParams.append(column, `${operator}.${value}`);\n    return this;\n  }\n}\nexports.default = PostgrestFilterBuilder;", "map": {"version": 3, "names": ["PostgrestTransformBuilder_1", "__importDefault", "require", "PostgrestFilterBuilder", "default", "eq", "column", "value", "url", "searchParams", "append", "neq", "gt", "gte", "lt", "lte", "like", "pattern", "likeAllOf", "patterns", "join", "likeAnyOf", "ilike", "ilikeAllOf", "ilikeAnyOf", "is", "in", "values", "cleanedV<PERSON>ues", "Array", "from", "Set", "map", "s", "RegExp", "test", "contains", "isArray", "JSON", "stringify", "containedBy", "rangeGt", "range", "rangeGte", "rangeLt", "rangeLte", "rangeAdja<PERSON>", "overlaps", "textSearch", "query", "config", "type", "typePart", "config<PERSON><PERSON>", "undefined", "match", "Object", "entries", "for<PERSON>ach", "not", "operator", "or", "filters", "foreignTable", "referencedTable", "key", "filter", "exports"], "sources": ["C:\\Users\\<USER>\\node_modules\\@supabase\\postgrest-js\\src\\PostgrestFilterBuilder.ts"], "sourcesContent": ["import PostgrestTransformBuilder from './PostgrestTransformBuilder'\nimport { JsonPathToAccessor, JsonPathToType } from './select-query-parser/utils'\nimport { GenericSchema } from './types'\n\ntype FilterOperator =\n  | 'eq'\n  | 'neq'\n  | 'gt'\n  | 'gte'\n  | 'lt'\n  | 'lte'\n  | 'like'\n  | 'ilike'\n  | 'is'\n  | 'in'\n  | 'cs'\n  | 'cd'\n  | 'sl'\n  | 'sr'\n  | 'nxl'\n  | 'nxr'\n  | 'adj'\n  | 'ov'\n  | 'fts'\n  | 'plfts'\n  | 'phfts'\n  | 'wfts'\n\nexport type IsStringOperator<Path extends string> = Path extends `${string}->>${string}`\n  ? true\n  : false\n\n// Match relationship filters with `table.column` syntax and resolve underlying\n// column value. If not matched, fallback to generic type.\n// TODO: Validate the relationship itself ala select-query-parser. Currently we\n// assume that all tables have valid relationships to each other, despite\n// nonexistent foreign keys.\ntype ResolveFilterValue<\n  Schema extends GenericSchema,\n  Row extends Record<string, unknown>,\n  ColumnName extends string\n> = ColumnName extends `${infer RelationshipTable}.${infer Remainder}`\n  ? Remainder extends `${infer _}.${infer _}`\n    ? ResolveFilterValue<Schema, Row, Remainder>\n    : ResolveFilterRelationshipValue<Schema, RelationshipTable, Remainder>\n  : ColumnName extends keyof Row\n  ? Row[ColumnName]\n  : // If the column selection is a jsonpath like `data->value` or `data->>value` we attempt to match\n  // the expected type with the parsed custom json type\n  IsStringOperator<ColumnName> extends true\n  ? string\n  : JsonPathToType<Row, JsonPathToAccessor<ColumnName>> extends infer JsonPathValue\n  ? JsonPathValue extends never\n    ? never\n    : JsonPathValue\n  : never\n\ntype ResolveFilterRelationshipValue<\n  Schema extends GenericSchema,\n  RelationshipTable extends string,\n  RelationshipColumn extends string\n> = Schema['Tables'] & Schema['Views'] extends infer TablesAndViews\n  ? RelationshipTable extends keyof TablesAndViews\n    ? 'Row' extends keyof TablesAndViews[RelationshipTable]\n      ? RelationshipColumn extends keyof TablesAndViews[RelationshipTable]['Row']\n        ? TablesAndViews[RelationshipTable]['Row'][RelationshipColumn]\n        : unknown\n      : unknown\n    : unknown\n  : never\n\nexport default class PostgrestFilterBuilder<\n  Schema extends GenericSchema,\n  Row extends Record<string, unknown>,\n  Result,\n  RelationName = unknown,\n  Relationships = unknown\n> extends PostgrestTransformBuilder<Schema, Row, Result, RelationName, Relationships> {\n  /**\n   * Match only rows where `column` is equal to `value`.\n   *\n   * To check if the value of `column` is NULL, you should use `.is()` instead.\n   *\n   * @param column - The column to filter on\n   * @param value - The value to filter with\n   */\n  eq<ColumnName extends string>(\n    column: ColumnName,\n    value: ResolveFilterValue<Schema, Row, ColumnName> extends never\n      ? NonNullable<unknown>\n      : // We want to infer the type before wrapping it into a `NonNullable` to avoid too deep\n      // type resolution error\n      ResolveFilterValue<Schema, Row, ColumnName> extends infer ResolvedFilterValue\n      ? NonNullable<ResolvedFilterValue>\n      : // We should never enter this case as all the branches are covered above\n        never\n  ): this {\n    this.url.searchParams.append(column, `eq.${value}`)\n    return this\n  }\n\n  /**\n   * Match only rows where `column` is not equal to `value`.\n   *\n   * @param column - The column to filter on\n   * @param value - The value to filter with\n   */\n  neq<ColumnName extends string>(\n    column: ColumnName,\n    value: ResolveFilterValue<Schema, Row, ColumnName> extends never\n      ? unknown\n      : ResolveFilterValue<Schema, Row, ColumnName> extends infer ResolvedFilterValue\n      ? ResolvedFilterValue\n      : never\n  ): this {\n    this.url.searchParams.append(column, `neq.${value}`)\n    return this\n  }\n\n  gt<ColumnName extends string & keyof Row>(column: ColumnName, value: Row[ColumnName]): this\n  gt(column: string, value: unknown): this\n  /**\n   * Match only rows where `column` is greater than `value`.\n   *\n   * @param column - The column to filter on\n   * @param value - The value to filter with\n   */\n  gt(column: string, value: unknown): this {\n    this.url.searchParams.append(column, `gt.${value}`)\n    return this\n  }\n\n  gte<ColumnName extends string & keyof Row>(column: ColumnName, value: Row[ColumnName]): this\n  gte(column: string, value: unknown): this\n  /**\n   * Match only rows where `column` is greater than or equal to `value`.\n   *\n   * @param column - The column to filter on\n   * @param value - The value to filter with\n   */\n  gte(column: string, value: unknown): this {\n    this.url.searchParams.append(column, `gte.${value}`)\n    return this\n  }\n\n  lt<ColumnName extends string & keyof Row>(column: ColumnName, value: Row[ColumnName]): this\n  lt(column: string, value: unknown): this\n  /**\n   * Match only rows where `column` is less than `value`.\n   *\n   * @param column - The column to filter on\n   * @param value - The value to filter with\n   */\n  lt(column: string, value: unknown): this {\n    this.url.searchParams.append(column, `lt.${value}`)\n    return this\n  }\n\n  lte<ColumnName extends string & keyof Row>(column: ColumnName, value: Row[ColumnName]): this\n  lte(column: string, value: unknown): this\n  /**\n   * Match only rows where `column` is less than or equal to `value`.\n   *\n   * @param column - The column to filter on\n   * @param value - The value to filter with\n   */\n  lte(column: string, value: unknown): this {\n    this.url.searchParams.append(column, `lte.${value}`)\n    return this\n  }\n\n  like<ColumnName extends string & keyof Row>(column: ColumnName, pattern: string): this\n  like(column: string, pattern: string): this\n  /**\n   * Match only rows where `column` matches `pattern` case-sensitively.\n   *\n   * @param column - The column to filter on\n   * @param pattern - The pattern to match with\n   */\n  like(column: string, pattern: string): this {\n    this.url.searchParams.append(column, `like.${pattern}`)\n    return this\n  }\n\n  likeAllOf<ColumnName extends string & keyof Row>(\n    column: ColumnName,\n    patterns: readonly string[]\n  ): this\n  likeAllOf(column: string, patterns: readonly string[]): this\n  /**\n   * Match only rows where `column` matches all of `patterns` case-sensitively.\n   *\n   * @param column - The column to filter on\n   * @param patterns - The patterns to match with\n   */\n  likeAllOf(column: string, patterns: readonly string[]): this {\n    this.url.searchParams.append(column, `like(all).{${patterns.join(',')}}`)\n    return this\n  }\n\n  likeAnyOf<ColumnName extends string & keyof Row>(\n    column: ColumnName,\n    patterns: readonly string[]\n  ): this\n  likeAnyOf(column: string, patterns: readonly string[]): this\n  /**\n   * Match only rows where `column` matches any of `patterns` case-sensitively.\n   *\n   * @param column - The column to filter on\n   * @param patterns - The patterns to match with\n   */\n  likeAnyOf(column: string, patterns: readonly string[]): this {\n    this.url.searchParams.append(column, `like(any).{${patterns.join(',')}}`)\n    return this\n  }\n\n  ilike<ColumnName extends string & keyof Row>(column: ColumnName, pattern: string): this\n  ilike(column: string, pattern: string): this\n  /**\n   * Match only rows where `column` matches `pattern` case-insensitively.\n   *\n   * @param column - The column to filter on\n   * @param pattern - The pattern to match with\n   */\n  ilike(column: string, pattern: string): this {\n    this.url.searchParams.append(column, `ilike.${pattern}`)\n    return this\n  }\n\n  ilikeAllOf<ColumnName extends string & keyof Row>(\n    column: ColumnName,\n    patterns: readonly string[]\n  ): this\n  ilikeAllOf(column: string, patterns: readonly string[]): this\n  /**\n   * Match only rows where `column` matches all of `patterns` case-insensitively.\n   *\n   * @param column - The column to filter on\n   * @param patterns - The patterns to match with\n   */\n  ilikeAllOf(column: string, patterns: readonly string[]): this {\n    this.url.searchParams.append(column, `ilike(all).{${patterns.join(',')}}`)\n    return this\n  }\n\n  ilikeAnyOf<ColumnName extends string & keyof Row>(\n    column: ColumnName,\n    patterns: readonly string[]\n  ): this\n  ilikeAnyOf(column: string, patterns: readonly string[]): this\n  /**\n   * Match only rows where `column` matches any of `patterns` case-insensitively.\n   *\n   * @param column - The column to filter on\n   * @param patterns - The patterns to match with\n   */\n  ilikeAnyOf(column: string, patterns: readonly string[]): this {\n    this.url.searchParams.append(column, `ilike(any).{${patterns.join(',')}}`)\n    return this\n  }\n\n  is<ColumnName extends string & keyof Row>(\n    column: ColumnName,\n    value: Row[ColumnName] & (boolean | null)\n  ): this\n  is(column: string, value: boolean | null): this\n  /**\n   * Match only rows where `column` IS `value`.\n   *\n   * For non-boolean columns, this is only relevant for checking if the value of\n   * `column` is NULL by setting `value` to `null`.\n   *\n   * For boolean columns, you can also set `value` to `true` or `false` and it\n   * will behave the same way as `.eq()`.\n   *\n   * @param column - The column to filter on\n   * @param value - The value to filter with\n   */\n  is(column: string, value: boolean | null): this {\n    this.url.searchParams.append(column, `is.${value}`)\n    return this\n  }\n\n  /**\n   * Match only rows where `column` is included in the `values` array.\n   *\n   * @param column - The column to filter on\n   * @param values - The values array to filter with\n   */\n  in<ColumnName extends string>(\n    column: ColumnName,\n    values: ReadonlyArray<\n      ResolveFilterValue<Schema, Row, ColumnName> extends never\n        ? unknown\n        : // We want to infer the type before wrapping it into a `NonNullable` to avoid too deep\n        // type resolution error\n        ResolveFilterValue<Schema, Row, ColumnName> extends infer ResolvedFilterValue\n        ? ResolvedFilterValue\n        : // We should never enter this case as all the branches are covered above\n          never\n    >\n  ): this {\n    const cleanedValues = Array.from(new Set(values))\n      .map((s) => {\n        // handle postgrest reserved characters\n        // https://postgrest.org/en/v7.0.0/api.html#reserved-characters\n        if (typeof s === 'string' && new RegExp('[,()]').test(s)) return `\"${s}\"`\n        else return `${s}`\n      })\n      .join(',')\n    this.url.searchParams.append(column, `in.(${cleanedValues})`)\n    return this\n  }\n\n  contains<ColumnName extends string & keyof Row>(\n    column: ColumnName,\n    value: string | ReadonlyArray<Row[ColumnName]> | Record<string, unknown>\n  ): this\n  contains(column: string, value: string | readonly unknown[] | Record<string, unknown>): this\n  /**\n   * Only relevant for jsonb, array, and range columns. Match only rows where\n   * `column` contains every element appearing in `value`.\n   *\n   * @param column - The jsonb, array, or range column to filter on\n   * @param value - The jsonb, array, or range value to filter with\n   */\n  contains(column: string, value: string | readonly unknown[] | Record<string, unknown>): this {\n    if (typeof value === 'string') {\n      // range types can be inclusive '[', ']' or exclusive '(', ')' so just\n      // keep it simple and accept a string\n      this.url.searchParams.append(column, `cs.${value}`)\n    } else if (Array.isArray(value)) {\n      // array\n      this.url.searchParams.append(column, `cs.{${value.join(',')}}`)\n    } else {\n      // json\n      this.url.searchParams.append(column, `cs.${JSON.stringify(value)}`)\n    }\n    return this\n  }\n\n  containedBy<ColumnName extends string & keyof Row>(\n    column: ColumnName,\n    value: string | ReadonlyArray<Row[ColumnName]> | Record<string, unknown>\n  ): this\n  containedBy(column: string, value: string | readonly unknown[] | Record<string, unknown>): this\n  /**\n   * Only relevant for jsonb, array, and range columns. Match only rows where\n   * every element appearing in `column` is contained by `value`.\n   *\n   * @param column - The jsonb, array, or range column to filter on\n   * @param value - The jsonb, array, or range value to filter with\n   */\n  containedBy(column: string, value: string | readonly unknown[] | Record<string, unknown>): this {\n    if (typeof value === 'string') {\n      // range\n      this.url.searchParams.append(column, `cd.${value}`)\n    } else if (Array.isArray(value)) {\n      // array\n      this.url.searchParams.append(column, `cd.{${value.join(',')}}`)\n    } else {\n      // json\n      this.url.searchParams.append(column, `cd.${JSON.stringify(value)}`)\n    }\n    return this\n  }\n\n  rangeGt<ColumnName extends string & keyof Row>(column: ColumnName, range: string): this\n  rangeGt(column: string, range: string): this\n  /**\n   * Only relevant for range columns. Match only rows where every element in\n   * `column` is greater than any element in `range`.\n   *\n   * @param column - The range column to filter on\n   * @param range - The range to filter with\n   */\n  rangeGt(column: string, range: string): this {\n    this.url.searchParams.append(column, `sr.${range}`)\n    return this\n  }\n\n  rangeGte<ColumnName extends string & keyof Row>(column: ColumnName, range: string): this\n  rangeGte(column: string, range: string): this\n  /**\n   * Only relevant for range columns. Match only rows where every element in\n   * `column` is either contained in `range` or greater than any element in\n   * `range`.\n   *\n   * @param column - The range column to filter on\n   * @param range - The range to filter with\n   */\n  rangeGte(column: string, range: string): this {\n    this.url.searchParams.append(column, `nxl.${range}`)\n    return this\n  }\n\n  rangeLt<ColumnName extends string & keyof Row>(column: ColumnName, range: string): this\n  rangeLt(column: string, range: string): this\n  /**\n   * Only relevant for range columns. Match only rows where every element in\n   * `column` is less than any element in `range`.\n   *\n   * @param column - The range column to filter on\n   * @param range - The range to filter with\n   */\n  rangeLt(column: string, range: string): this {\n    this.url.searchParams.append(column, `sl.${range}`)\n    return this\n  }\n\n  rangeLte<ColumnName extends string & keyof Row>(column: ColumnName, range: string): this\n  rangeLte(column: string, range: string): this\n  /**\n   * Only relevant for range columns. Match only rows where every element in\n   * `column` is either contained in `range` or less than any element in\n   * `range`.\n   *\n   * @param column - The range column to filter on\n   * @param range - The range to filter with\n   */\n  rangeLte(column: string, range: string): this {\n    this.url.searchParams.append(column, `nxr.${range}`)\n    return this\n  }\n\n  rangeAdjacent<ColumnName extends string & keyof Row>(column: ColumnName, range: string): this\n  rangeAdjacent(column: string, range: string): this\n  /**\n   * Only relevant for range columns. Match only rows where `column` is\n   * mutually exclusive to `range` and there can be no element between the two\n   * ranges.\n   *\n   * @param column - The range column to filter on\n   * @param range - The range to filter with\n   */\n  rangeAdjacent(column: string, range: string): this {\n    this.url.searchParams.append(column, `adj.${range}`)\n    return this\n  }\n\n  overlaps<ColumnName extends string & keyof Row>(\n    column: ColumnName,\n    value: string | ReadonlyArray<Row[ColumnName]>\n  ): this\n  overlaps(column: string, value: string | readonly unknown[]): this\n  /**\n   * Only relevant for array and range columns. Match only rows where\n   * `column` and `value` have an element in common.\n   *\n   * @param column - The array or range column to filter on\n   * @param value - The array or range value to filter with\n   */\n  overlaps(column: string, value: string | readonly unknown[]): this {\n    if (typeof value === 'string') {\n      // range\n      this.url.searchParams.append(column, `ov.${value}`)\n    } else {\n      // array\n      this.url.searchParams.append(column, `ov.{${value.join(',')}}`)\n    }\n    return this\n  }\n\n  textSearch<ColumnName extends string & keyof Row>(\n    column: ColumnName,\n    query: string,\n    options?: { config?: string; type?: 'plain' | 'phrase' | 'websearch' }\n  ): this\n  textSearch(\n    column: string,\n    query: string,\n    options?: { config?: string; type?: 'plain' | 'phrase' | 'websearch' }\n  ): this\n  /**\n   * Only relevant for text and tsvector columns. Match only rows where\n   * `column` matches the query string in `query`.\n   *\n   * @param column - The text or tsvector column to filter on\n   * @param query - The query text to match with\n   * @param options - Named parameters\n   * @param options.config - The text search configuration to use\n   * @param options.type - Change how the `query` text is interpreted\n   */\n  textSearch(\n    column: string,\n    query: string,\n    { config, type }: { config?: string; type?: 'plain' | 'phrase' | 'websearch' } = {}\n  ): this {\n    let typePart = ''\n    if (type === 'plain') {\n      typePart = 'pl'\n    } else if (type === 'phrase') {\n      typePart = 'ph'\n    } else if (type === 'websearch') {\n      typePart = 'w'\n    }\n    const configPart = config === undefined ? '' : `(${config})`\n    this.url.searchParams.append(column, `${typePart}fts${configPart}.${query}`)\n    return this\n  }\n\n  match<ColumnName extends string & keyof Row>(query: Record<ColumnName, Row[ColumnName]>): this\n  match(query: Record<string, unknown>): this\n  /**\n   * Match only rows where each column in `query` keys is equal to its\n   * associated value. Shorthand for multiple `.eq()`s.\n   *\n   * @param query - The object to filter with, with column names as keys mapped\n   * to their filter values\n   */\n  match(query: Record<string, unknown>): this {\n    Object.entries(query).forEach(([column, value]) => {\n      this.url.searchParams.append(column, `eq.${value}`)\n    })\n    return this\n  }\n\n  not<ColumnName extends string & keyof Row>(\n    column: ColumnName,\n    operator: FilterOperator,\n    value: Row[ColumnName]\n  ): this\n  not(column: string, operator: string, value: unknown): this\n  /**\n   * Match only rows which doesn't satisfy the filter.\n   *\n   * Unlike most filters, `opearator` and `value` are used as-is and need to\n   * follow [PostgREST\n   * syntax](https://postgrest.org/en/stable/api.html#operators). You also need\n   * to make sure they are properly sanitized.\n   *\n   * @param column - The column to filter on\n   * @param operator - The operator to be negated to filter with, following\n   * PostgREST syntax\n   * @param value - The value to filter with, following PostgREST syntax\n   */\n  not(column: string, operator: string, value: unknown): this {\n    this.url.searchParams.append(column, `not.${operator}.${value}`)\n    return this\n  }\n\n  /**\n   * Match only rows which satisfy at least one of the filters.\n   *\n   * Unlike most filters, `filters` is used as-is and needs to follow [PostgREST\n   * syntax](https://postgrest.org/en/stable/api.html#operators). You also need\n   * to make sure it's properly sanitized.\n   *\n   * It's currently not possible to do an `.or()` filter across multiple tables.\n   *\n   * @param filters - The filters to use, following PostgREST syntax\n   * @param options - Named parameters\n   * @param options.referencedTable - Set this to filter on referenced tables\n   * instead of the parent table\n   * @param options.foreignTable - Deprecated, use `referencedTable` instead\n   */\n  or(\n    filters: string,\n    {\n      foreignTable,\n      referencedTable = foreignTable,\n    }: { foreignTable?: string; referencedTable?: string } = {}\n  ): this {\n    const key = referencedTable ? `${referencedTable}.or` : 'or'\n    this.url.searchParams.append(key, `(${filters})`)\n    return this\n  }\n\n  filter<ColumnName extends string & keyof Row>(\n    column: ColumnName,\n    operator: `${'' | 'not.'}${FilterOperator}`,\n    value: unknown\n  ): this\n  filter(column: string, operator: string, value: unknown): this\n  /**\n   * Match only rows which satisfy the filter. This is an escape hatch - you\n   * should use the specific filter methods wherever possible.\n   *\n   * Unlike most filters, `opearator` and `value` are used as-is and need to\n   * follow [PostgREST\n   * syntax](https://postgrest.org/en/stable/api.html#operators). You also need\n   * to make sure they are properly sanitized.\n   *\n   * @param column - The column to filter on\n   * @param operator - The operator to filter with, following PostgREST syntax\n   * @param value - The value to filter with, following PostgREST syntax\n   */\n  filter(column: string, operator: string, value: unknown): this {\n    this.url.searchParams.append(column, `${operator}.${value}`)\n    return this\n  }\n}\n"], "mappings": ";;;;;;;;;;AAAA,MAAAA,2BAAA,GAAAC,eAAA,CAAAC,OAAA;AAuEA,MAAqBC,sBAMnB,SAAQH,2BAAA,CAAAI,OAA2E;EACnF;;;;;;;;EAQAC,EAAEA,CACAC,MAAkB,EAClBC,KAOS;IAET,IAAI,CAACC,GAAG,CAACC,YAAY,CAACC,MAAM,CAACJ,MAAM,EAAE,MAAMC,KAAK,EAAE,CAAC;IACnD,OAAO,IAAI;EACb;EAEA;;;;;;EAMAI,GAAGA,CACDL,MAAkB,EAClBC,KAIS;IAET,IAAI,CAACC,GAAG,CAACC,YAAY,CAACC,MAAM,CAACJ,MAAM,EAAE,OAAOC,KAAK,EAAE,CAAC;IACpD,OAAO,IAAI;EACb;EAIA;;;;;;EAMAK,EAAEA,CAACN,MAAc,EAAEC,KAAc;IAC/B,IAAI,CAACC,GAAG,CAACC,YAAY,CAACC,MAAM,CAACJ,MAAM,EAAE,MAAMC,KAAK,EAAE,CAAC;IACnD,OAAO,IAAI;EACb;EAIA;;;;;;EAMAM,GAAGA,CAACP,MAAc,EAAEC,KAAc;IAChC,IAAI,CAACC,GAAG,CAACC,YAAY,CAACC,MAAM,CAACJ,MAAM,EAAE,OAAOC,KAAK,EAAE,CAAC;IACpD,OAAO,IAAI;EACb;EAIA;;;;;;EAMAO,EAAEA,CAACR,MAAc,EAAEC,KAAc;IAC/B,IAAI,CAACC,GAAG,CAACC,YAAY,CAACC,MAAM,CAACJ,MAAM,EAAE,MAAMC,KAAK,EAAE,CAAC;IACnD,OAAO,IAAI;EACb;EAIA;;;;;;EAMAQ,GAAGA,CAACT,MAAc,EAAEC,KAAc;IAChC,IAAI,CAACC,GAAG,CAACC,YAAY,CAACC,MAAM,CAACJ,MAAM,EAAE,OAAOC,KAAK,EAAE,CAAC;IACpD,OAAO,IAAI;EACb;EAIA;;;;;;EAMAS,IAAIA,CAACV,MAAc,EAAEW,OAAe;IAClC,IAAI,CAACT,GAAG,CAACC,YAAY,CAACC,MAAM,CAACJ,MAAM,EAAE,QAAQW,OAAO,EAAE,CAAC;IACvD,OAAO,IAAI;EACb;EAOA;;;;;;EAMAC,SAASA,CAACZ,MAAc,EAAEa,QAA2B;IACnD,IAAI,CAACX,GAAG,CAACC,YAAY,CAACC,MAAM,CAACJ,MAAM,EAAE,cAAca,QAAQ,CAACC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC;IACzE,OAAO,IAAI;EACb;EAOA;;;;;;EAMAC,SAASA,CAACf,MAAc,EAAEa,QAA2B;IACnD,IAAI,CAACX,GAAG,CAACC,YAAY,CAACC,MAAM,CAACJ,MAAM,EAAE,cAAca,QAAQ,CAACC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC;IACzE,OAAO,IAAI;EACb;EAIA;;;;;;EAMAE,KAAKA,CAAChB,MAAc,EAAEW,OAAe;IACnC,IAAI,CAACT,GAAG,CAACC,YAAY,CAACC,MAAM,CAACJ,MAAM,EAAE,SAASW,OAAO,EAAE,CAAC;IACxD,OAAO,IAAI;EACb;EAOA;;;;;;EAMAM,UAAUA,CAACjB,MAAc,EAAEa,QAA2B;IACpD,IAAI,CAACX,GAAG,CAACC,YAAY,CAACC,MAAM,CAACJ,MAAM,EAAE,eAAea,QAAQ,CAACC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC;IAC1E,OAAO,IAAI;EACb;EAOA;;;;;;EAMAI,UAAUA,CAAClB,MAAc,EAAEa,QAA2B;IACpD,IAAI,CAACX,GAAG,CAACC,YAAY,CAACC,MAAM,CAACJ,MAAM,EAAE,eAAea,QAAQ,CAACC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC;IAC1E,OAAO,IAAI;EACb;EAOA;;;;;;;;;;;;EAYAK,EAAEA,CAACnB,MAAc,EAAEC,KAAqB;IACtC,IAAI,CAACC,GAAG,CAACC,YAAY,CAACC,MAAM,CAACJ,MAAM,EAAE,MAAMC,KAAK,EAAE,CAAC;IACnD,OAAO,IAAI;EACb;EAEA;;;;;;EAMAmB,EAAEA,CACApB,MAAkB,EAClBqB,MASC;IAED,MAAMC,aAAa,GAAGC,KAAK,CAACC,IAAI,CAAC,IAAIC,GAAG,CAACJ,MAAM,CAAC,CAAC,CAC9CK,GAAG,CAAEC,CAAC,IAAI;MACT;MACA;MACA,IAAI,OAAOA,CAAC,KAAK,QAAQ,IAAI,IAAIC,MAAM,CAAC,OAAO,CAAC,CAACC,IAAI,CAACF,CAAC,CAAC,EAAE,OAAO,IAAIA,CAAC,GAAG,MACpE,OAAO,GAAGA,CAAC,EAAE;IACpB,CAAC,CAAC,CACDb,IAAI,CAAC,GAAG,CAAC;IACZ,IAAI,CAACZ,GAAG,CAACC,YAAY,CAACC,MAAM,CAACJ,MAAM,EAAE,OAAOsB,aAAa,GAAG,CAAC;IAC7D,OAAO,IAAI;EACb;EAOA;;;;;;;EAOAQ,QAAQA,CAAC9B,MAAc,EAAEC,KAA4D;IACnF,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;MAC7B;MACA;MACA,IAAI,CAACC,GAAG,CAACC,YAAY,CAACC,MAAM,CAACJ,MAAM,EAAE,MAAMC,KAAK,EAAE,CAAC;KACpD,MAAM,IAAIsB,KAAK,CAACQ,OAAO,CAAC9B,KAAK,CAAC,EAAE;MAC/B;MACA,IAAI,CAACC,GAAG,CAACC,YAAY,CAACC,MAAM,CAACJ,MAAM,EAAE,OAAOC,KAAK,CAACa,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC;KAChE,MAAM;MACL;MACA,IAAI,CAACZ,GAAG,CAACC,YAAY,CAACC,MAAM,CAACJ,MAAM,EAAE,MAAMgC,IAAI,CAACC,SAAS,CAAChC,KAAK,CAAC,EAAE,CAAC;;IAErE,OAAO,IAAI;EACb;EAOA;;;;;;;EAOAiC,WAAWA,CAAClC,MAAc,EAAEC,KAA4D;IACtF,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;MAC7B;MACA,IAAI,CAACC,GAAG,CAACC,YAAY,CAACC,MAAM,CAACJ,MAAM,EAAE,MAAMC,KAAK,EAAE,CAAC;KACpD,MAAM,IAAIsB,KAAK,CAACQ,OAAO,CAAC9B,KAAK,CAAC,EAAE;MAC/B;MACA,IAAI,CAACC,GAAG,CAACC,YAAY,CAACC,MAAM,CAACJ,MAAM,EAAE,OAAOC,KAAK,CAACa,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC;KAChE,MAAM;MACL;MACA,IAAI,CAACZ,GAAG,CAACC,YAAY,CAACC,MAAM,CAACJ,MAAM,EAAE,MAAMgC,IAAI,CAACC,SAAS,CAAChC,KAAK,CAAC,EAAE,CAAC;;IAErE,OAAO,IAAI;EACb;EAIA;;;;;;;EAOAkC,OAAOA,CAACnC,MAAc,EAAEoC,KAAa;IACnC,IAAI,CAAClC,GAAG,CAACC,YAAY,CAACC,MAAM,CAACJ,MAAM,EAAE,MAAMoC,KAAK,EAAE,CAAC;IACnD,OAAO,IAAI;EACb;EAIA;;;;;;;;EAQAC,QAAQA,CAACrC,MAAc,EAAEoC,KAAa;IACpC,IAAI,CAAClC,GAAG,CAACC,YAAY,CAACC,MAAM,CAACJ,MAAM,EAAE,OAAOoC,KAAK,EAAE,CAAC;IACpD,OAAO,IAAI;EACb;EAIA;;;;;;;EAOAE,OAAOA,CAACtC,MAAc,EAAEoC,KAAa;IACnC,IAAI,CAAClC,GAAG,CAACC,YAAY,CAACC,MAAM,CAACJ,MAAM,EAAE,MAAMoC,KAAK,EAAE,CAAC;IACnD,OAAO,IAAI;EACb;EAIA;;;;;;;;EAQAG,QAAQA,CAACvC,MAAc,EAAEoC,KAAa;IACpC,IAAI,CAAClC,GAAG,CAACC,YAAY,CAACC,MAAM,CAACJ,MAAM,EAAE,OAAOoC,KAAK,EAAE,CAAC;IACpD,OAAO,IAAI;EACb;EAIA;;;;;;;;EAQAI,aAAaA,CAACxC,MAAc,EAAEoC,KAAa;IACzC,IAAI,CAAClC,GAAG,CAACC,YAAY,CAACC,MAAM,CAACJ,MAAM,EAAE,OAAOoC,KAAK,EAAE,CAAC;IACpD,OAAO,IAAI;EACb;EAOA;;;;;;;EAOAK,QAAQA,CAACzC,MAAc,EAAEC,KAAkC;IACzD,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;MAC7B;MACA,IAAI,CAACC,GAAG,CAACC,YAAY,CAACC,MAAM,CAACJ,MAAM,EAAE,MAAMC,KAAK,EAAE,CAAC;KACpD,MAAM;MACL;MACA,IAAI,CAACC,GAAG,CAACC,YAAY,CAACC,MAAM,CAACJ,MAAM,EAAE,OAAOC,KAAK,CAACa,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC;;IAEjE,OAAO,IAAI;EACb;EAYA;;;;;;;;;;EAUA4B,UAAUA,CACR1C,MAAc,EACd2C,KAAa,EACb;IAAEC,MAAM;IAAEC;EAAI,IAAmE,EAAE;IAEnF,IAAIC,QAAQ,GAAG,EAAE;IACjB,IAAID,IAAI,KAAK,OAAO,EAAE;MACpBC,QAAQ,GAAG,IAAI;KAChB,MAAM,IAAID,IAAI,KAAK,QAAQ,EAAE;MAC5BC,QAAQ,GAAG,IAAI;KAChB,MAAM,IAAID,IAAI,KAAK,WAAW,EAAE;MAC/BC,QAAQ,GAAG,GAAG;;IAEhB,MAAMC,UAAU,GAAGH,MAAM,KAAKI,SAAS,GAAG,EAAE,GAAG,IAAIJ,MAAM,GAAG;IAC5D,IAAI,CAAC1C,GAAG,CAACC,YAAY,CAACC,MAAM,CAACJ,MAAM,EAAE,GAAG8C,QAAQ,MAAMC,UAAU,IAAIJ,KAAK,EAAE,CAAC;IAC5E,OAAO,IAAI;EACb;EAIA;;;;;;;EAOAM,KAAKA,CAACN,KAA8B;IAClCO,MAAM,CAACC,OAAO,CAACR,KAAK,CAAC,CAACS,OAAO,CAAC,CAAC,CAACpD,MAAM,EAAEC,KAAK,CAAC,KAAI;MAChD,IAAI,CAACC,GAAG,CAACC,YAAY,CAACC,MAAM,CAACJ,MAAM,EAAE,MAAMC,KAAK,EAAE,CAAC;IACrD,CAAC,CAAC;IACF,OAAO,IAAI;EACb;EAQA;;;;;;;;;;;;;EAaAoD,GAAGA,CAACrD,MAAc,EAAEsD,QAAgB,EAAErD,KAAc;IAClD,IAAI,CAACC,GAAG,CAACC,YAAY,CAACC,MAAM,CAACJ,MAAM,EAAE,OAAOsD,QAAQ,IAAIrD,KAAK,EAAE,CAAC;IAChE,OAAO,IAAI;EACb;EAEA;;;;;;;;;;;;;;;EAeAsD,EAAEA,CACAC,OAAe,EACf;IACEC,YAAY;IACZC,eAAe,GAAGD;EAAY,IACyB,EAAE;IAE3D,MAAME,GAAG,GAAGD,eAAe,GAAG,GAAGA,eAAe,KAAK,GAAG,IAAI;IAC5D,IAAI,CAACxD,GAAG,CAACC,YAAY,CAACC,MAAM,CAACuD,GAAG,EAAE,IAAIH,OAAO,GAAG,CAAC;IACjD,OAAO,IAAI;EACb;EAQA;;;;;;;;;;;;;EAaAI,MAAMA,CAAC5D,MAAc,EAAEsD,QAAgB,EAAErD,KAAc;IACrD,IAAI,CAACC,GAAG,CAACC,YAAY,CAACC,MAAM,CAACJ,MAAM,EAAE,GAAGsD,QAAQ,IAAIrD,KAAK,EAAE,CAAC;IAC5D,OAAO,IAAI;EACb;;AAvgBF4D,OAAA,CAAA/D,OAAA,GAAAD,sBAAA", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}