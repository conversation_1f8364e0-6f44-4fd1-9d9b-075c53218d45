{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\lostandfound\\\\discfinder-app\\\\src\\\\contexts\\\\AuthContext.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useEffect, useState } from 'react';\nimport { supabase } from '../lib/supabase';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AuthContext = /*#__PURE__*/createContext(undefined);\nexport const useAuth = () => {\n  _s();\n  const context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n_s(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport const AuthProvider = ({\n  children\n}) => {\n  _s2();\n  const [user, setUser] = useState(null);\n  const [profile, setProfile] = useState(null);\n  const [session, setSession] = useState(null);\n  const [userRole, setUserRole] = useState('guest');\n  const [loading, setLoading] = useState(true);\n  useEffect(() => {\n    // Get initial session with timeout\n    const initAuth = async () => {\n      try {\n        var _session$user;\n        console.log('Initializing auth...');\n        const {\n          data: {\n            session\n          },\n          error\n        } = await supabase.auth.getSession();\n        if (error) {\n          console.warn('Auth session error:', error);\n          setUserRole('guest');\n          setLoading(false);\n          return;\n        }\n        setSession(session);\n        setUser((_session$user = session === null || session === void 0 ? void 0 : session.user) !== null && _session$user !== void 0 ? _session$user : null);\n        if (session !== null && session !== void 0 && session.user) {\n          console.log('User found, fetching profile...');\n          await fetchProfile(session.user.id);\n        } else {\n          console.log('No user session, setting as guest');\n          setUserRole('guest');\n          setLoading(false);\n        }\n      } catch (error) {\n        console.warn('Auth initialization failed:', error);\n        setUserRole('guest');\n        setLoading(false);\n      }\n    };\n\n    // Set a timeout to prevent infinite loading\n    const timeoutId = setTimeout(() => {\n      console.warn('Auth initialization timeout, setting as guest');\n      setUserRole('guest');\n      setLoading(false);\n    }, 5000); // 5 second timeout\n\n    initAuth().finally(() => {\n      clearTimeout(timeoutId);\n    });\n\n    // Listen for auth changes\n    const {\n      data: {\n        subscription\n      }\n    } = supabase.auth.onAuthStateChange(async (event, session) => {\n      var _session$user2;\n      setSession(session);\n      setUser((_session$user2 = session === null || session === void 0 ? void 0 : session.user) !== null && _session$user2 !== void 0 ? _session$user2 : null);\n      if (session !== null && session !== void 0 && session.user) {\n        await fetchProfile(session.user.id);\n      } else {\n        setProfile(null);\n        setUserRole('guest');\n        setLoading(false);\n      }\n    });\n    return () => subscription.unsubscribe();\n  }, []);\n  const fetchProfile = async userId => {\n    try {\n      const {\n        data,\n        error\n      } = await supabase.from('profiles').select('*').eq('id', userId).single();\n      if (error) {\n        // If profile doesn't exist, create it\n        if (error.code === 'PGRST116') {\n          console.log('Profile not found, creating one...');\n          try {\n            const {\n              data: userData\n            } = await supabase.auth.getUser();\n            if (userData.user) {\n              var _userData$user$user_m;\n              await supabase.from('profiles').insert([{\n                id: userData.user.id,\n                email: userData.user.email,\n                full_name: ((_userData$user$user_m = userData.user.user_metadata) === null || _userData$user$user_m === void 0 ? void 0 : _userData$user$user_m.full_name) || '',\n                role: 'user',\n                created_at: new Date().toISOString(),\n                updated_at: new Date().toISOString()\n              }]);\n\n              // Retry fetching the profile\n              const {\n                data: newProfile\n              } = await supabase.from('profiles').select('*').eq('id', userId).single();\n              if (newProfile) {\n                setProfile(newProfile);\n                setUserRole(newProfile.role || 'user');\n                setLoading(false);\n                return;\n              }\n            }\n          } catch (createError) {\n            console.warn('Failed to create profile:', createError);\n          }\n        }\n        console.error('Error fetching profile:', error);\n        setUserRole('guest');\n        setLoading(false);\n        return;\n      }\n      setProfile(data);\n      setUserRole(data.role || 'user');\n      setLoading(false);\n    } catch (error) {\n      console.error('Error fetching profile:', error);\n      setUserRole('guest');\n      setLoading(false);\n    }\n  };\n  const signUp = async (email, password, fullName) => {\n    try {\n      const {\n        data,\n        error\n      } = await supabase.auth.signUp({\n        email,\n        password,\n        options: {\n          data: {\n            full_name: fullName\n          }\n        }\n      });\n\n      // If user creation succeeded, manually create the profile\n      if (data.user && !error) {\n        try {\n          await supabase.from('profiles').insert([{\n            id: data.user.id,\n            email: data.user.email,\n            full_name: fullName,\n            role: 'user',\n            created_at: new Date().toISOString(),\n            updated_at: new Date().toISOString()\n          }]);\n        } catch (profileError) {\n          console.warn('Profile creation failed, but user was created:', profileError);\n          // Don't fail the signup if profile creation fails\n        }\n      }\n      return {\n        data,\n        error\n      };\n    } catch (error) {\n      return {\n        data: null,\n        error: {\n          message: 'Supabase not configured. This is a demo.'\n        }\n      };\n    }\n  };\n  const signIn = async (email, password) => {\n    try {\n      const {\n        data,\n        error\n      } = await supabase.auth.signInWithPassword({\n        email,\n        password\n      });\n      return {\n        data,\n        error\n      };\n    } catch (error) {\n      return {\n        data: null,\n        error: {\n          message: 'Supabase not configured. This is a demo.'\n        }\n      };\n    }\n  };\n  const signOut = async () => {\n    try {\n      const {\n        error\n      } = await supabase.auth.signOut();\n      if (error) {\n        console.error('Error signing out:', error);\n      }\n    } catch (error) {\n      console.log('Supabase not configured, running in demo mode');\n    }\n  };\n  const updateProfile = async updates => {\n    if (!user) return;\n    try {\n      const {\n        error\n      } = await supabase.from('profiles').update(updates).eq('id', user.id);\n      if (error) {\n        throw error;\n      }\n\n      // Refresh profile\n      await fetchProfile(user.id);\n    } catch (error) {\n      console.error('Error updating profile:', error);\n      throw error;\n    }\n  };\n  const value = {\n    user,\n    profile,\n    session,\n    userRole,\n    loading,\n    signUp,\n    signIn,\n    signOut,\n    updateProfile,\n    isGuest: userRole === 'guest',\n    isUser: userRole === 'user',\n    isAdmin: userRole === 'admin'\n  };\n  return /*#__PURE__*/_jsxDEV(AuthContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 260,\n    columnNumber: 5\n  }, this);\n};\n_s2(AuthProvider, \"clRRS62CUjhjIMIlA9YAjluDkG8=\");\n_c = AuthProvider;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useEffect", "useState", "supabase", "jsxDEV", "_jsxDEV", "AuthContext", "undefined", "useAuth", "_s", "context", "Error", "<PERSON>th<PERSON><PERSON><PERSON>", "children", "_s2", "user", "setUser", "profile", "setProfile", "session", "setSession", "userRole", "setUserRole", "loading", "setLoading", "initAuth", "_session$user", "console", "log", "data", "error", "auth", "getSession", "warn", "fetchProfile", "id", "timeoutId", "setTimeout", "finally", "clearTimeout", "subscription", "onAuthStateChange", "event", "_session$user2", "unsubscribe", "userId", "from", "select", "eq", "single", "code", "userData", "getUser", "_userData$user$user_m", "insert", "email", "full_name", "user_metadata", "role", "created_at", "Date", "toISOString", "updated_at", "newProfile", "createError", "signUp", "password", "fullName", "options", "profileError", "message", "signIn", "signInWithPassword", "signOut", "updateProfile", "updates", "update", "value", "isGuest", "isUser", "isAdmin", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/lostandfound/discfinder-app/src/contexts/AuthContext.tsx"], "sourcesContent": ["import React, { createContext, useContext, useEffect, useState } from 'react';\nimport { User, Session } from '@supabase/supabase-js';\nimport { supabase, Profile } from '../lib/supabase';\n\nexport type UserRole = 'guest' | 'user' | 'admin';\n\ninterface AuthContextType {\n  user: User | null;\n  profile: Profile | null;\n  session: Session | null;\n  userRole: UserRole;\n  loading: boolean;\n  signUp: (email: string, password: string, fullName: string) => Promise<any>;\n  signIn: (email: string, password: string) => Promise<any>;\n  signOut: () => Promise<void>;\n  updateProfile: (updates: Partial<Profile>) => Promise<void>;\n  isGuest: boolean;\n  isUser: boolean;\n  isAdmin: boolean;\n}\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined);\n\nexport const useAuth = () => {\n  const context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n\nexport const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {\n  const [user, setUser] = useState<User | null>(null);\n  const [profile, setProfile] = useState<Profile | null>(null);\n  const [session, setSession] = useState<Session | null>(null);\n  const [userRole, setUserRole] = useState<UserRole>('guest');\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    // Get initial session with timeout\n    const initAuth = async () => {\n      try {\n        console.log('Initializing auth...');\n        const { data: { session }, error } = await supabase.auth.getSession();\n\n        if (error) {\n          console.warn('Auth session error:', error);\n          setUserRole('guest');\n          setLoading(false);\n          return;\n        }\n\n        setSession(session);\n        setUser(session?.user ?? null);\n\n        if (session?.user) {\n          console.log('User found, fetching profile...');\n          await fetchProfile(session.user.id);\n        } else {\n          console.log('No user session, setting as guest');\n          setUserRole('guest');\n          setLoading(false);\n        }\n      } catch (error) {\n        console.warn('Auth initialization failed:', error);\n        setUserRole('guest');\n        setLoading(false);\n      }\n    };\n\n    // Set a timeout to prevent infinite loading\n    const timeoutId = setTimeout(() => {\n      console.warn('Auth initialization timeout, setting as guest');\n      setUserRole('guest');\n      setLoading(false);\n    }, 5000); // 5 second timeout\n\n    initAuth().finally(() => {\n      clearTimeout(timeoutId);\n    });\n\n    // Listen for auth changes\n    const {\n      data: { subscription },\n    } = supabase.auth.onAuthStateChange(async (event, session) => {\n      setSession(session);\n      setUser(session?.user ?? null);\n      \n      if (session?.user) {\n        await fetchProfile(session.user.id);\n      } else {\n        setProfile(null);\n        setUserRole('guest');\n        setLoading(false);\n      }\n    });\n\n    return () => subscription.unsubscribe();\n  }, []);\n\n  const fetchProfile = async (userId: string) => {\n    try {\n      const { data, error } = await supabase\n        .from('profiles')\n        .select('*')\n        .eq('id', userId)\n        .single();\n\n      if (error) {\n        // If profile doesn't exist, create it\n        if (error.code === 'PGRST116') {\n          console.log('Profile not found, creating one...');\n          try {\n            const { data: userData } = await supabase.auth.getUser();\n            if (userData.user) {\n              await supabase\n                .from('profiles')\n                .insert([{\n                  id: userData.user.id,\n                  email: userData.user.email!,\n                  full_name: userData.user.user_metadata?.full_name || '',\n                  role: 'user',\n                  created_at: new Date().toISOString(),\n                  updated_at: new Date().toISOString()\n                }]);\n\n              // Retry fetching the profile\n              const { data: newProfile } = await supabase\n                .from('profiles')\n                .select('*')\n                .eq('id', userId)\n                .single();\n\n              if (newProfile) {\n                setProfile(newProfile);\n                setUserRole(newProfile.role || 'user');\n                setLoading(false);\n                return;\n              }\n            }\n          } catch (createError) {\n            console.warn('Failed to create profile:', createError);\n          }\n        }\n\n        console.error('Error fetching profile:', error);\n        setUserRole('guest');\n        setLoading(false);\n        return;\n      }\n\n      setProfile(data);\n      setUserRole(data.role || 'user');\n      setLoading(false);\n    } catch (error) {\n      console.error('Error fetching profile:', error);\n      setUserRole('guest');\n      setLoading(false);\n    }\n  };\n\n  const signUp = async (email: string, password: string, fullName: string) => {\n    try {\n      const { data, error } = await supabase.auth.signUp({\n        email,\n        password,\n        options: {\n          data: {\n            full_name: fullName,\n          },\n        },\n      });\n\n      // If user creation succeeded, manually create the profile\n      if (data.user && !error) {\n        try {\n          await supabase\n            .from('profiles')\n            .insert([{\n              id: data.user.id,\n              email: data.user.email!,\n              full_name: fullName,\n              role: 'user',\n              created_at: new Date().toISOString(),\n              updated_at: new Date().toISOString()\n            }]);\n        } catch (profileError) {\n          console.warn('Profile creation failed, but user was created:', profileError);\n          // Don't fail the signup if profile creation fails\n        }\n      }\n\n      return { data, error };\n    } catch (error) {\n      return { data: null, error: { message: 'Supabase not configured. This is a demo.' } };\n    }\n  };\n\n  const signIn = async (email: string, password: string) => {\n    try {\n      const { data, error } = await supabase.auth.signInWithPassword({\n        email,\n        password,\n      });\n\n      return { data, error };\n    } catch (error) {\n      return { data: null, error: { message: 'Supabase not configured. This is a demo.' } };\n    }\n  };\n\n  const signOut = async () => {\n    try {\n      const { error } = await supabase.auth.signOut();\n      if (error) {\n        console.error('Error signing out:', error);\n      }\n    } catch (error) {\n      console.log('Supabase not configured, running in demo mode');\n    }\n  };\n\n  const updateProfile = async (updates: Partial<Profile>) => {\n    if (!user) return;\n\n    try {\n      const { error } = await supabase\n        .from('profiles')\n        .update(updates)\n        .eq('id', user.id);\n\n      if (error) {\n        throw error;\n      }\n\n      // Refresh profile\n      await fetchProfile(user.id);\n    } catch (error) {\n      console.error('Error updating profile:', error);\n      throw error;\n    }\n  };\n\n  const value = {\n    user,\n    profile,\n    session,\n    userRole,\n    loading,\n    signUp,\n    signIn,\n    signOut,\n    updateProfile,\n    isGuest: userRole === 'guest',\n    isUser: userRole === 'user',\n    isAdmin: userRole === 'admin',\n  };\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  );\n};\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAE7E,SAASC,QAAQ,QAAiB,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAmBpD,MAAMC,WAAW,gBAAGP,aAAa,CAA8BQ,SAAS,CAAC;AAEzE,OAAO,MAAMC,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAMC,OAAO,GAAGV,UAAU,CAACM,WAAW,CAAC;EACvC,IAAII,OAAO,KAAKH,SAAS,EAAE;IACzB,MAAM,IAAII,KAAK,CAAC,6CAA6C,CAAC;EAChE;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,EAAA,CANWD,OAAO;AAQpB,OAAO,MAAMI,YAAqD,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,GAAA;EACrF,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGd,QAAQ,CAAc,IAAI,CAAC;EACnD,MAAM,CAACe,OAAO,EAAEC,UAAU,CAAC,GAAGhB,QAAQ,CAAiB,IAAI,CAAC;EAC5D,MAAM,CAACiB,OAAO,EAAEC,UAAU,CAAC,GAAGlB,QAAQ,CAAiB,IAAI,CAAC;EAC5D,MAAM,CAACmB,QAAQ,EAAEC,WAAW,CAAC,GAAGpB,QAAQ,CAAW,OAAO,CAAC;EAC3D,MAAM,CAACqB,OAAO,EAAEC,UAAU,CAAC,GAAGtB,QAAQ,CAAC,IAAI,CAAC;EAE5CD,SAAS,CAAC,MAAM;IACd;IACA,MAAMwB,QAAQ,GAAG,MAAAA,CAAA,KAAY;MAC3B,IAAI;QAAA,IAAAC,aAAA;QACFC,OAAO,CAACC,GAAG,CAAC,sBAAsB,CAAC;QACnC,MAAM;UAAEC,IAAI,EAAE;YAAEV;UAAQ,CAAC;UAAEW;QAAM,CAAC,GAAG,MAAM3B,QAAQ,CAAC4B,IAAI,CAACC,UAAU,CAAC,CAAC;QAErE,IAAIF,KAAK,EAAE;UACTH,OAAO,CAACM,IAAI,CAAC,qBAAqB,EAAEH,KAAK,CAAC;UAC1CR,WAAW,CAAC,OAAO,CAAC;UACpBE,UAAU,CAAC,KAAK,CAAC;UACjB;QACF;QAEAJ,UAAU,CAACD,OAAO,CAAC;QACnBH,OAAO,EAAAU,aAAA,GAACP,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEJ,IAAI,cAAAW,aAAA,cAAAA,aAAA,GAAI,IAAI,CAAC;QAE9B,IAAIP,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEJ,IAAI,EAAE;UACjBY,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;UAC9C,MAAMM,YAAY,CAACf,OAAO,CAACJ,IAAI,CAACoB,EAAE,CAAC;QACrC,CAAC,MAAM;UACLR,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;UAChDN,WAAW,CAAC,OAAO,CAAC;UACpBE,UAAU,CAAC,KAAK,CAAC;QACnB;MACF,CAAC,CAAC,OAAOM,KAAK,EAAE;QACdH,OAAO,CAACM,IAAI,CAAC,6BAA6B,EAAEH,KAAK,CAAC;QAClDR,WAAW,CAAC,OAAO,CAAC;QACpBE,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;;IAED;IACA,MAAMY,SAAS,GAAGC,UAAU,CAAC,MAAM;MACjCV,OAAO,CAACM,IAAI,CAAC,+CAA+C,CAAC;MAC7DX,WAAW,CAAC,OAAO,CAAC;MACpBE,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;;IAEVC,QAAQ,CAAC,CAAC,CAACa,OAAO,CAAC,MAAM;MACvBC,YAAY,CAACH,SAAS,CAAC;IACzB,CAAC,CAAC;;IAEF;IACA,MAAM;MACJP,IAAI,EAAE;QAAEW;MAAa;IACvB,CAAC,GAAGrC,QAAQ,CAAC4B,IAAI,CAACU,iBAAiB,CAAC,OAAOC,KAAK,EAAEvB,OAAO,KAAK;MAAA,IAAAwB,cAAA;MAC5DvB,UAAU,CAACD,OAAO,CAAC;MACnBH,OAAO,EAAA2B,cAAA,GAACxB,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEJ,IAAI,cAAA4B,cAAA,cAAAA,cAAA,GAAI,IAAI,CAAC;MAE9B,IAAIxB,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEJ,IAAI,EAAE;QACjB,MAAMmB,YAAY,CAACf,OAAO,CAACJ,IAAI,CAACoB,EAAE,CAAC;MACrC,CAAC,MAAM;QACLjB,UAAU,CAAC,IAAI,CAAC;QAChBI,WAAW,CAAC,OAAO,CAAC;QACpBE,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC,CAAC;IAEF,OAAO,MAAMgB,YAAY,CAACI,WAAW,CAAC,CAAC;EACzC,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMV,YAAY,GAAG,MAAOW,MAAc,IAAK;IAC7C,IAAI;MACF,MAAM;QAAEhB,IAAI;QAAEC;MAAM,CAAC,GAAG,MAAM3B,QAAQ,CACnC2C,IAAI,CAAC,UAAU,CAAC,CAChBC,MAAM,CAAC,GAAG,CAAC,CACXC,EAAE,CAAC,IAAI,EAAEH,MAAM,CAAC,CAChBI,MAAM,CAAC,CAAC;MAEX,IAAInB,KAAK,EAAE;QACT;QACA,IAAIA,KAAK,CAACoB,IAAI,KAAK,UAAU,EAAE;UAC7BvB,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;UACjD,IAAI;YACF,MAAM;cAAEC,IAAI,EAAEsB;YAAS,CAAC,GAAG,MAAMhD,QAAQ,CAAC4B,IAAI,CAACqB,OAAO,CAAC,CAAC;YACxD,IAAID,QAAQ,CAACpC,IAAI,EAAE;cAAA,IAAAsC,qBAAA;cACjB,MAAMlD,QAAQ,CACX2C,IAAI,CAAC,UAAU,CAAC,CAChBQ,MAAM,CAAC,CAAC;gBACPnB,EAAE,EAAEgB,QAAQ,CAACpC,IAAI,CAACoB,EAAE;gBACpBoB,KAAK,EAAEJ,QAAQ,CAACpC,IAAI,CAACwC,KAAM;gBAC3BC,SAAS,EAAE,EAAAH,qBAAA,GAAAF,QAAQ,CAACpC,IAAI,CAAC0C,aAAa,cAAAJ,qBAAA,uBAA3BA,qBAAA,CAA6BG,SAAS,KAAI,EAAE;gBACvDE,IAAI,EAAE,MAAM;gBACZC,UAAU,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;gBACpCC,UAAU,EAAE,IAAIF,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;cACrC,CAAC,CAAC,CAAC;;cAEL;cACA,MAAM;gBAAEhC,IAAI,EAAEkC;cAAW,CAAC,GAAG,MAAM5D,QAAQ,CACxC2C,IAAI,CAAC,UAAU,CAAC,CAChBC,MAAM,CAAC,GAAG,CAAC,CACXC,EAAE,CAAC,IAAI,EAAEH,MAAM,CAAC,CAChBI,MAAM,CAAC,CAAC;cAEX,IAAIc,UAAU,EAAE;gBACd7C,UAAU,CAAC6C,UAAU,CAAC;gBACtBzC,WAAW,CAACyC,UAAU,CAACL,IAAI,IAAI,MAAM,CAAC;gBACtClC,UAAU,CAAC,KAAK,CAAC;gBACjB;cACF;YACF;UACF,CAAC,CAAC,OAAOwC,WAAW,EAAE;YACpBrC,OAAO,CAACM,IAAI,CAAC,2BAA2B,EAAE+B,WAAW,CAAC;UACxD;QACF;QAEArC,OAAO,CAACG,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;QAC/CR,WAAW,CAAC,OAAO,CAAC;QACpBE,UAAU,CAAC,KAAK,CAAC;QACjB;MACF;MAEAN,UAAU,CAACW,IAAI,CAAC;MAChBP,WAAW,CAACO,IAAI,CAAC6B,IAAI,IAAI,MAAM,CAAC;MAChClC,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC,CAAC,OAAOM,KAAK,EAAE;MACdH,OAAO,CAACG,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/CR,WAAW,CAAC,OAAO,CAAC;MACpBE,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMyC,MAAM,GAAG,MAAAA,CAAOV,KAAa,EAAEW,QAAgB,EAAEC,QAAgB,KAAK;IAC1E,IAAI;MACF,MAAM;QAAEtC,IAAI;QAAEC;MAAM,CAAC,GAAG,MAAM3B,QAAQ,CAAC4B,IAAI,CAACkC,MAAM,CAAC;QACjDV,KAAK;QACLW,QAAQ;QACRE,OAAO,EAAE;UACPvC,IAAI,EAAE;YACJ2B,SAAS,EAAEW;UACb;QACF;MACF,CAAC,CAAC;;MAEF;MACA,IAAItC,IAAI,CAACd,IAAI,IAAI,CAACe,KAAK,EAAE;QACvB,IAAI;UACF,MAAM3B,QAAQ,CACX2C,IAAI,CAAC,UAAU,CAAC,CAChBQ,MAAM,CAAC,CAAC;YACPnB,EAAE,EAAEN,IAAI,CAACd,IAAI,CAACoB,EAAE;YAChBoB,KAAK,EAAE1B,IAAI,CAACd,IAAI,CAACwC,KAAM;YACvBC,SAAS,EAAEW,QAAQ;YACnBT,IAAI,EAAE,MAAM;YACZC,UAAU,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;YACpCC,UAAU,EAAE,IAAIF,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;UACrC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,OAAOQ,YAAY,EAAE;UACrB1C,OAAO,CAACM,IAAI,CAAC,gDAAgD,EAAEoC,YAAY,CAAC;UAC5E;QACF;MACF;MAEA,OAAO;QAAExC,IAAI;QAAEC;MAAM,CAAC;IACxB,CAAC,CAAC,OAAOA,KAAK,EAAE;MACd,OAAO;QAAED,IAAI,EAAE,IAAI;QAAEC,KAAK,EAAE;UAAEwC,OAAO,EAAE;QAA2C;MAAE,CAAC;IACvF;EACF,CAAC;EAED,MAAMC,MAAM,GAAG,MAAAA,CAAOhB,KAAa,EAAEW,QAAgB,KAAK;IACxD,IAAI;MACF,MAAM;QAAErC,IAAI;QAAEC;MAAM,CAAC,GAAG,MAAM3B,QAAQ,CAAC4B,IAAI,CAACyC,kBAAkB,CAAC;QAC7DjB,KAAK;QACLW;MACF,CAAC,CAAC;MAEF,OAAO;QAAErC,IAAI;QAAEC;MAAM,CAAC;IACxB,CAAC,CAAC,OAAOA,KAAK,EAAE;MACd,OAAO;QAAED,IAAI,EAAE,IAAI;QAAEC,KAAK,EAAE;UAAEwC,OAAO,EAAE;QAA2C;MAAE,CAAC;IACvF;EACF,CAAC;EAED,MAAMG,OAAO,GAAG,MAAAA,CAAA,KAAY;IAC1B,IAAI;MACF,MAAM;QAAE3C;MAAM,CAAC,GAAG,MAAM3B,QAAQ,CAAC4B,IAAI,CAAC0C,OAAO,CAAC,CAAC;MAC/C,IAAI3C,KAAK,EAAE;QACTH,OAAO,CAACG,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC5C;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdH,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;IAC9D;EACF,CAAC;EAED,MAAM8C,aAAa,GAAG,MAAOC,OAAyB,IAAK;IACzD,IAAI,CAAC5D,IAAI,EAAE;IAEX,IAAI;MACF,MAAM;QAAEe;MAAM,CAAC,GAAG,MAAM3B,QAAQ,CAC7B2C,IAAI,CAAC,UAAU,CAAC,CAChB8B,MAAM,CAACD,OAAO,CAAC,CACf3B,EAAE,CAAC,IAAI,EAAEjC,IAAI,CAACoB,EAAE,CAAC;MAEpB,IAAIL,KAAK,EAAE;QACT,MAAMA,KAAK;MACb;;MAEA;MACA,MAAMI,YAAY,CAACnB,IAAI,CAACoB,EAAE,CAAC;IAC7B,CAAC,CAAC,OAAOL,KAAK,EAAE;MACdH,OAAO,CAACG,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/C,MAAMA,KAAK;IACb;EACF,CAAC;EAED,MAAM+C,KAAK,GAAG;IACZ9D,IAAI;IACJE,OAAO;IACPE,OAAO;IACPE,QAAQ;IACRE,OAAO;IACP0C,MAAM;IACNM,MAAM;IACNE,OAAO;IACPC,aAAa;IACbI,OAAO,EAAEzD,QAAQ,KAAK,OAAO;IAC7B0D,MAAM,EAAE1D,QAAQ,KAAK,MAAM;IAC3B2D,OAAO,EAAE3D,QAAQ,KAAK;EACxB,CAAC;EAED,oBACEhB,OAAA,CAACC,WAAW,CAAC2E,QAAQ;IAACJ,KAAK,EAAEA,KAAM;IAAAhE,QAAA,EAChCA;EAAQ;IAAAqE,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACW,CAAC;AAE3B,CAAC;AAACvE,GAAA,CAxOWF,YAAqD;AAAA0E,EAAA,GAArD1E,YAAqD;AAAA,IAAA0E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}