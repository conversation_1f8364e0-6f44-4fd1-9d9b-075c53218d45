{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\lostandfound\\\\discfinder-app\\\\src\\\\contexts\\\\AuthContext.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useEffect, useState } from 'react';\nimport { supabase } from '../lib/supabase';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AuthContext = /*#__PURE__*/createContext(undefined);\nexport const useAuth = () => {\n  _s();\n  const context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n_s(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport const AuthProvider = ({\n  children\n}) => {\n  _s2();\n  const [user, setUser] = useState(null);\n  const [profile, setProfile] = useState(null);\n  const [session, setSession] = useState(null);\n  const [userRole, setUserRole] = useState('guest');\n  const [loading, setLoading] = useState(true);\n  useEffect(() => {\n    // Get initial session\n    supabase.auth.getSession().then(({\n      data: {\n        session\n      }\n    }) => {\n      var _session$user;\n      setSession(session);\n      setUser((_session$user = session === null || session === void 0 ? void 0 : session.user) !== null && _session$user !== void 0 ? _session$user : null);\n      if (session !== null && session !== void 0 && session.user) {\n        fetchProfile(session.user.id);\n      } else {\n        setUserRole('guest');\n        setLoading(false);\n      }\n    }).catch(() => {\n      // If Supabase isn't configured, just set loading to false\n      setLoading(false);\n    });\n\n    // Listen for auth changes\n    const {\n      data: {\n        subscription\n      }\n    } = supabase.auth.onAuthStateChange(async (event, session) => {\n      var _session$user2;\n      setSession(session);\n      setUser((_session$user2 = session === null || session === void 0 ? void 0 : session.user) !== null && _session$user2 !== void 0 ? _session$user2 : null);\n      if (session !== null && session !== void 0 && session.user) {\n        await fetchProfile(session.user.id);\n      } else {\n        setProfile(null);\n        setUserRole('guest');\n        setLoading(false);\n      }\n    });\n    return () => subscription.unsubscribe();\n  }, []);\n  const fetchProfile = async userId => {\n    try {\n      const {\n        data,\n        error\n      } = await supabase.from('profiles').select('*').eq('id', userId).single();\n      if (error) {\n        // If profile doesn't exist, create it\n        if (error.code === 'PGRST116') {\n          console.log('Profile not found, creating one...');\n          try {\n            const {\n              data: userData\n            } = await supabase.auth.getUser();\n            if (userData.user) {\n              var _userData$user$user_m;\n              await supabase.from('profiles').insert([{\n                id: userData.user.id,\n                email: userData.user.email,\n                full_name: ((_userData$user$user_m = userData.user.user_metadata) === null || _userData$user$user_m === void 0 ? void 0 : _userData$user$user_m.full_name) || '',\n                role: 'user',\n                created_at: new Date().toISOString(),\n                updated_at: new Date().toISOString()\n              }]);\n\n              // Retry fetching the profile\n              const {\n                data: newProfile\n              } = await supabase.from('profiles').select('*').eq('id', userId).single();\n              if (newProfile) {\n                setProfile(newProfile);\n                setUserRole(newProfile.role || 'user');\n                setLoading(false);\n                return;\n              }\n            }\n          } catch (createError) {\n            console.warn('Failed to create profile:', createError);\n          }\n        }\n        console.error('Error fetching profile:', error);\n        setUserRole('guest');\n        setLoading(false);\n        return;\n      }\n      setProfile(data);\n      setUserRole(data.role || 'user');\n      setLoading(false);\n    } catch (error) {\n      console.error('Error fetching profile:', error);\n      setUserRole('guest');\n      setLoading(false);\n    }\n  };\n  const signUp = async (email, password, fullName) => {\n    try {\n      const {\n        data,\n        error\n      } = await supabase.auth.signUp({\n        email,\n        password,\n        options: {\n          data: {\n            full_name: fullName\n          }\n        }\n      });\n\n      // If user creation succeeded, manually create the profile\n      if (data.user && !error) {\n        try {\n          await supabase.from('profiles').insert([{\n            id: data.user.id,\n            email: data.user.email,\n            full_name: fullName,\n            role: 'user',\n            created_at: new Date().toISOString(),\n            updated_at: new Date().toISOString()\n          }]);\n        } catch (profileError) {\n          console.warn('Profile creation failed, but user was created:', profileError);\n          // Don't fail the signup if profile creation fails\n        }\n      }\n      return {\n        data,\n        error\n      };\n    } catch (error) {\n      return {\n        data: null,\n        error: {\n          message: 'Supabase not configured. This is a demo.'\n        }\n      };\n    }\n  };\n  const signIn = async (email, password) => {\n    try {\n      const {\n        data,\n        error\n      } = await supabase.auth.signInWithPassword({\n        email,\n        password\n      });\n      return {\n        data,\n        error\n      };\n    } catch (error) {\n      return {\n        data: null,\n        error: {\n          message: 'Supabase not configured. This is a demo.'\n        }\n      };\n    }\n  };\n  const signOut = async () => {\n    try {\n      const {\n        error\n      } = await supabase.auth.signOut();\n      if (error) {\n        console.error('Error signing out:', error);\n      }\n    } catch (error) {\n      console.log('Supabase not configured, running in demo mode');\n    }\n  };\n  const updateProfile = async updates => {\n    if (!user) return;\n    try {\n      const {\n        error\n      } = await supabase.from('profiles').update(updates).eq('id', user.id);\n      if (error) {\n        throw error;\n      }\n\n      // Refresh profile\n      await fetchProfile(user.id);\n    } catch (error) {\n      console.error('Error updating profile:', error);\n      throw error;\n    }\n  };\n  const value = {\n    user,\n    profile,\n    session,\n    userRole,\n    loading,\n    signUp,\n    signIn,\n    signOut,\n    updateProfile,\n    isGuest: userRole === 'guest',\n    isUser: userRole === 'user',\n    isAdmin: userRole === 'admin'\n  };\n  return /*#__PURE__*/_jsxDEV(AuthContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 233,\n    columnNumber: 5\n  }, this);\n};\n_s2(AuthProvider, \"clRRS62CUjhjIMIlA9YAjluDkG8=\");\n_c = AuthProvider;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useEffect", "useState", "supabase", "jsxDEV", "_jsxDEV", "AuthContext", "undefined", "useAuth", "_s", "context", "Error", "<PERSON>th<PERSON><PERSON><PERSON>", "children", "_s2", "user", "setUser", "profile", "setProfile", "session", "setSession", "userRole", "setUserRole", "loading", "setLoading", "auth", "getSession", "then", "data", "_session$user", "fetchProfile", "id", "catch", "subscription", "onAuthStateChange", "event", "_session$user2", "unsubscribe", "userId", "error", "from", "select", "eq", "single", "code", "console", "log", "userData", "getUser", "_userData$user$user_m", "insert", "email", "full_name", "user_metadata", "role", "created_at", "Date", "toISOString", "updated_at", "newProfile", "createError", "warn", "signUp", "password", "fullName", "options", "profileError", "message", "signIn", "signInWithPassword", "signOut", "updateProfile", "updates", "update", "value", "isGuest", "isUser", "isAdmin", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/lostandfound/discfinder-app/src/contexts/AuthContext.tsx"], "sourcesContent": ["import React, { createContext, useContext, useEffect, useState } from 'react';\nimport { User, Session } from '@supabase/supabase-js';\nimport { supabase, Profile } from '../lib/supabase';\n\nexport type UserRole = 'guest' | 'user' | 'admin';\n\ninterface AuthContextType {\n  user: User | null;\n  profile: Profile | null;\n  session: Session | null;\n  userRole: UserRole;\n  loading: boolean;\n  signUp: (email: string, password: string, fullName: string) => Promise<any>;\n  signIn: (email: string, password: string) => Promise<any>;\n  signOut: () => Promise<void>;\n  updateProfile: (updates: Partial<Profile>) => Promise<void>;\n  isGuest: boolean;\n  isUser: boolean;\n  isAdmin: boolean;\n}\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined);\n\nexport const useAuth = () => {\n  const context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n\nexport const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {\n  const [user, setUser] = useState<User | null>(null);\n  const [profile, setProfile] = useState<Profile | null>(null);\n  const [session, setSession] = useState<Session | null>(null);\n  const [userRole, setUserRole] = useState<UserRole>('guest');\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    // Get initial session\n    supabase.auth.getSession().then(({ data: { session } }) => {\n      setSession(session);\n      setUser(session?.user ?? null);\n      if (session?.user) {\n        fetchProfile(session.user.id);\n      } else {\n        setUserRole('guest');\n        setLoading(false);\n      }\n    }).catch(() => {\n      // If Supabase isn't configured, just set loading to false\n      setLoading(false);\n    });\n\n    // Listen for auth changes\n    const {\n      data: { subscription },\n    } = supabase.auth.onAuthStateChange(async (event, session) => {\n      setSession(session);\n      setUser(session?.user ?? null);\n      \n      if (session?.user) {\n        await fetchProfile(session.user.id);\n      } else {\n        setProfile(null);\n        setUserRole('guest');\n        setLoading(false);\n      }\n    });\n\n    return () => subscription.unsubscribe();\n  }, []);\n\n  const fetchProfile = async (userId: string) => {\n    try {\n      const { data, error } = await supabase\n        .from('profiles')\n        .select('*')\n        .eq('id', userId)\n        .single();\n\n      if (error) {\n        // If profile doesn't exist, create it\n        if (error.code === 'PGRST116') {\n          console.log('Profile not found, creating one...');\n          try {\n            const { data: userData } = await supabase.auth.getUser();\n            if (userData.user) {\n              await supabase\n                .from('profiles')\n                .insert([{\n                  id: userData.user.id,\n                  email: userData.user.email!,\n                  full_name: userData.user.user_metadata?.full_name || '',\n                  role: 'user',\n                  created_at: new Date().toISOString(),\n                  updated_at: new Date().toISOString()\n                }]);\n\n              // Retry fetching the profile\n              const { data: newProfile } = await supabase\n                .from('profiles')\n                .select('*')\n                .eq('id', userId)\n                .single();\n\n              if (newProfile) {\n                setProfile(newProfile);\n                setUserRole(newProfile.role || 'user');\n                setLoading(false);\n                return;\n              }\n            }\n          } catch (createError) {\n            console.warn('Failed to create profile:', createError);\n          }\n        }\n\n        console.error('Error fetching profile:', error);\n        setUserRole('guest');\n        setLoading(false);\n        return;\n      }\n\n      setProfile(data);\n      setUserRole(data.role || 'user');\n      setLoading(false);\n    } catch (error) {\n      console.error('Error fetching profile:', error);\n      setUserRole('guest');\n      setLoading(false);\n    }\n  };\n\n  const signUp = async (email: string, password: string, fullName: string) => {\n    try {\n      const { data, error } = await supabase.auth.signUp({\n        email,\n        password,\n        options: {\n          data: {\n            full_name: fullName,\n          },\n        },\n      });\n\n      // If user creation succeeded, manually create the profile\n      if (data.user && !error) {\n        try {\n          await supabase\n            .from('profiles')\n            .insert([{\n              id: data.user.id,\n              email: data.user.email!,\n              full_name: fullName,\n              role: 'user',\n              created_at: new Date().toISOString(),\n              updated_at: new Date().toISOString()\n            }]);\n        } catch (profileError) {\n          console.warn('Profile creation failed, but user was created:', profileError);\n          // Don't fail the signup if profile creation fails\n        }\n      }\n\n      return { data, error };\n    } catch (error) {\n      return { data: null, error: { message: 'Supabase not configured. This is a demo.' } };\n    }\n  };\n\n  const signIn = async (email: string, password: string) => {\n    try {\n      const { data, error } = await supabase.auth.signInWithPassword({\n        email,\n        password,\n      });\n\n      return { data, error };\n    } catch (error) {\n      return { data: null, error: { message: 'Supabase not configured. This is a demo.' } };\n    }\n  };\n\n  const signOut = async () => {\n    try {\n      const { error } = await supabase.auth.signOut();\n      if (error) {\n        console.error('Error signing out:', error);\n      }\n    } catch (error) {\n      console.log('Supabase not configured, running in demo mode');\n    }\n  };\n\n  const updateProfile = async (updates: Partial<Profile>) => {\n    if (!user) return;\n\n    try {\n      const { error } = await supabase\n        .from('profiles')\n        .update(updates)\n        .eq('id', user.id);\n\n      if (error) {\n        throw error;\n      }\n\n      // Refresh profile\n      await fetchProfile(user.id);\n    } catch (error) {\n      console.error('Error updating profile:', error);\n      throw error;\n    }\n  };\n\n  const value = {\n    user,\n    profile,\n    session,\n    userRole,\n    loading,\n    signUp,\n    signIn,\n    signOut,\n    updateProfile,\n    isGuest: userRole === 'guest',\n    isUser: userRole === 'user',\n    isAdmin: userRole === 'admin',\n  };\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  );\n};\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAE7E,SAASC,QAAQ,QAAiB,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAmBpD,MAAMC,WAAW,gBAAGP,aAAa,CAA8BQ,SAAS,CAAC;AAEzE,OAAO,MAAMC,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAMC,OAAO,GAAGV,UAAU,CAACM,WAAW,CAAC;EACvC,IAAII,OAAO,KAAKH,SAAS,EAAE;IACzB,MAAM,IAAII,KAAK,CAAC,6CAA6C,CAAC;EAChE;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,EAAA,CANWD,OAAO;AAQpB,OAAO,MAAMI,YAAqD,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,GAAA;EACrF,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGd,QAAQ,CAAc,IAAI,CAAC;EACnD,MAAM,CAACe,OAAO,EAAEC,UAAU,CAAC,GAAGhB,QAAQ,CAAiB,IAAI,CAAC;EAC5D,MAAM,CAACiB,OAAO,EAAEC,UAAU,CAAC,GAAGlB,QAAQ,CAAiB,IAAI,CAAC;EAC5D,MAAM,CAACmB,QAAQ,EAAEC,WAAW,CAAC,GAAGpB,QAAQ,CAAW,OAAO,CAAC;EAC3D,MAAM,CAACqB,OAAO,EAAEC,UAAU,CAAC,GAAGtB,QAAQ,CAAC,IAAI,CAAC;EAE5CD,SAAS,CAAC,MAAM;IACd;IACAE,QAAQ,CAACsB,IAAI,CAACC,UAAU,CAAC,CAAC,CAACC,IAAI,CAAC,CAAC;MAAEC,IAAI,EAAE;QAAET;MAAQ;IAAE,CAAC,KAAK;MAAA,IAAAU,aAAA;MACzDT,UAAU,CAACD,OAAO,CAAC;MACnBH,OAAO,EAAAa,aAAA,GAACV,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEJ,IAAI,cAAAc,aAAA,cAAAA,aAAA,GAAI,IAAI,CAAC;MAC9B,IAAIV,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEJ,IAAI,EAAE;QACjBe,YAAY,CAACX,OAAO,CAACJ,IAAI,CAACgB,EAAE,CAAC;MAC/B,CAAC,MAAM;QACLT,WAAW,CAAC,OAAO,CAAC;QACpBE,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC,CAAC,CAACQ,KAAK,CAAC,MAAM;MACb;MACAR,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC,CAAC;;IAEF;IACA,MAAM;MACJI,IAAI,EAAE;QAAEK;MAAa;IACvB,CAAC,GAAG9B,QAAQ,CAACsB,IAAI,CAACS,iBAAiB,CAAC,OAAOC,KAAK,EAAEhB,OAAO,KAAK;MAAA,IAAAiB,cAAA;MAC5DhB,UAAU,CAACD,OAAO,CAAC;MACnBH,OAAO,EAAAoB,cAAA,GAACjB,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEJ,IAAI,cAAAqB,cAAA,cAAAA,cAAA,GAAI,IAAI,CAAC;MAE9B,IAAIjB,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEJ,IAAI,EAAE;QACjB,MAAMe,YAAY,CAACX,OAAO,CAACJ,IAAI,CAACgB,EAAE,CAAC;MACrC,CAAC,MAAM;QACLb,UAAU,CAAC,IAAI,CAAC;QAChBI,WAAW,CAAC,OAAO,CAAC;QACpBE,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC,CAAC;IAEF,OAAO,MAAMS,YAAY,CAACI,WAAW,CAAC,CAAC;EACzC,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMP,YAAY,GAAG,MAAOQ,MAAc,IAAK;IAC7C,IAAI;MACF,MAAM;QAAEV,IAAI;QAAEW;MAAM,CAAC,GAAG,MAAMpC,QAAQ,CACnCqC,IAAI,CAAC,UAAU,CAAC,CAChBC,MAAM,CAAC,GAAG,CAAC,CACXC,EAAE,CAAC,IAAI,EAAEJ,MAAM,CAAC,CAChBK,MAAM,CAAC,CAAC;MAEX,IAAIJ,KAAK,EAAE;QACT;QACA,IAAIA,KAAK,CAACK,IAAI,KAAK,UAAU,EAAE;UAC7BC,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;UACjD,IAAI;YACF,MAAM;cAAElB,IAAI,EAAEmB;YAAS,CAAC,GAAG,MAAM5C,QAAQ,CAACsB,IAAI,CAACuB,OAAO,CAAC,CAAC;YACxD,IAAID,QAAQ,CAAChC,IAAI,EAAE;cAAA,IAAAkC,qBAAA;cACjB,MAAM9C,QAAQ,CACXqC,IAAI,CAAC,UAAU,CAAC,CAChBU,MAAM,CAAC,CAAC;gBACPnB,EAAE,EAAEgB,QAAQ,CAAChC,IAAI,CAACgB,EAAE;gBACpBoB,KAAK,EAAEJ,QAAQ,CAAChC,IAAI,CAACoC,KAAM;gBAC3BC,SAAS,EAAE,EAAAH,qBAAA,GAAAF,QAAQ,CAAChC,IAAI,CAACsC,aAAa,cAAAJ,qBAAA,uBAA3BA,qBAAA,CAA6BG,SAAS,KAAI,EAAE;gBACvDE,IAAI,EAAE,MAAM;gBACZC,UAAU,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;gBACpCC,UAAU,EAAE,IAAIF,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;cACrC,CAAC,CAAC,CAAC;;cAEL;cACA,MAAM;gBAAE7B,IAAI,EAAE+B;cAAW,CAAC,GAAG,MAAMxD,QAAQ,CACxCqC,IAAI,CAAC,UAAU,CAAC,CAChBC,MAAM,CAAC,GAAG,CAAC,CACXC,EAAE,CAAC,IAAI,EAAEJ,MAAM,CAAC,CAChBK,MAAM,CAAC,CAAC;cAEX,IAAIgB,UAAU,EAAE;gBACdzC,UAAU,CAACyC,UAAU,CAAC;gBACtBrC,WAAW,CAACqC,UAAU,CAACL,IAAI,IAAI,MAAM,CAAC;gBACtC9B,UAAU,CAAC,KAAK,CAAC;gBACjB;cACF;YACF;UACF,CAAC,CAAC,OAAOoC,WAAW,EAAE;YACpBf,OAAO,CAACgB,IAAI,CAAC,2BAA2B,EAAED,WAAW,CAAC;UACxD;QACF;QAEAf,OAAO,CAACN,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;QAC/CjB,WAAW,CAAC,OAAO,CAAC;QACpBE,UAAU,CAAC,KAAK,CAAC;QACjB;MACF;MAEAN,UAAU,CAACU,IAAI,CAAC;MAChBN,WAAW,CAACM,IAAI,CAAC0B,IAAI,IAAI,MAAM,CAAC;MAChC9B,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC,CAAC,OAAOe,KAAK,EAAE;MACdM,OAAO,CAACN,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/CjB,WAAW,CAAC,OAAO,CAAC;MACpBE,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMsC,MAAM,GAAG,MAAAA,CAAOX,KAAa,EAAEY,QAAgB,EAAEC,QAAgB,KAAK;IAC1E,IAAI;MACF,MAAM;QAAEpC,IAAI;QAAEW;MAAM,CAAC,GAAG,MAAMpC,QAAQ,CAACsB,IAAI,CAACqC,MAAM,CAAC;QACjDX,KAAK;QACLY,QAAQ;QACRE,OAAO,EAAE;UACPrC,IAAI,EAAE;YACJwB,SAAS,EAAEY;UACb;QACF;MACF,CAAC,CAAC;;MAEF;MACA,IAAIpC,IAAI,CAACb,IAAI,IAAI,CAACwB,KAAK,EAAE;QACvB,IAAI;UACF,MAAMpC,QAAQ,CACXqC,IAAI,CAAC,UAAU,CAAC,CAChBU,MAAM,CAAC,CAAC;YACPnB,EAAE,EAAEH,IAAI,CAACb,IAAI,CAACgB,EAAE;YAChBoB,KAAK,EAAEvB,IAAI,CAACb,IAAI,CAACoC,KAAM;YACvBC,SAAS,EAAEY,QAAQ;YACnBV,IAAI,EAAE,MAAM;YACZC,UAAU,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;YACpCC,UAAU,EAAE,IAAIF,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;UACrC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,OAAOS,YAAY,EAAE;UACrBrB,OAAO,CAACgB,IAAI,CAAC,gDAAgD,EAAEK,YAAY,CAAC;UAC5E;QACF;MACF;MAEA,OAAO;QAAEtC,IAAI;QAAEW;MAAM,CAAC;IACxB,CAAC,CAAC,OAAOA,KAAK,EAAE;MACd,OAAO;QAAEX,IAAI,EAAE,IAAI;QAAEW,KAAK,EAAE;UAAE4B,OAAO,EAAE;QAA2C;MAAE,CAAC;IACvF;EACF,CAAC;EAED,MAAMC,MAAM,GAAG,MAAAA,CAAOjB,KAAa,EAAEY,QAAgB,KAAK;IACxD,IAAI;MACF,MAAM;QAAEnC,IAAI;QAAEW;MAAM,CAAC,GAAG,MAAMpC,QAAQ,CAACsB,IAAI,CAAC4C,kBAAkB,CAAC;QAC7DlB,KAAK;QACLY;MACF,CAAC,CAAC;MAEF,OAAO;QAAEnC,IAAI;QAAEW;MAAM,CAAC;IACxB,CAAC,CAAC,OAAOA,KAAK,EAAE;MACd,OAAO;QAAEX,IAAI,EAAE,IAAI;QAAEW,KAAK,EAAE;UAAE4B,OAAO,EAAE;QAA2C;MAAE,CAAC;IACvF;EACF,CAAC;EAED,MAAMG,OAAO,GAAG,MAAAA,CAAA,KAAY;IAC1B,IAAI;MACF,MAAM;QAAE/B;MAAM,CAAC,GAAG,MAAMpC,QAAQ,CAACsB,IAAI,CAAC6C,OAAO,CAAC,CAAC;MAC/C,IAAI/B,KAAK,EAAE;QACTM,OAAO,CAACN,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC5C;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdM,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;IAC9D;EACF,CAAC;EAED,MAAMyB,aAAa,GAAG,MAAOC,OAAyB,IAAK;IACzD,IAAI,CAACzD,IAAI,EAAE;IAEX,IAAI;MACF,MAAM;QAAEwB;MAAM,CAAC,GAAG,MAAMpC,QAAQ,CAC7BqC,IAAI,CAAC,UAAU,CAAC,CAChBiC,MAAM,CAACD,OAAO,CAAC,CACf9B,EAAE,CAAC,IAAI,EAAE3B,IAAI,CAACgB,EAAE,CAAC;MAEpB,IAAIQ,KAAK,EAAE;QACT,MAAMA,KAAK;MACb;;MAEA;MACA,MAAMT,YAAY,CAACf,IAAI,CAACgB,EAAE,CAAC;IAC7B,CAAC,CAAC,OAAOQ,KAAK,EAAE;MACdM,OAAO,CAACN,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/C,MAAMA,KAAK;IACb;EACF,CAAC;EAED,MAAMmC,KAAK,GAAG;IACZ3D,IAAI;IACJE,OAAO;IACPE,OAAO;IACPE,QAAQ;IACRE,OAAO;IACPuC,MAAM;IACNM,MAAM;IACNE,OAAO;IACPC,aAAa;IACbI,OAAO,EAAEtD,QAAQ,KAAK,OAAO;IAC7BuD,MAAM,EAAEvD,QAAQ,KAAK,MAAM;IAC3BwD,OAAO,EAAExD,QAAQ,KAAK;EACxB,CAAC;EAED,oBACEhB,OAAA,CAACC,WAAW,CAACwE,QAAQ;IAACJ,KAAK,EAAEA,KAAM;IAAA7D,QAAA,EAChCA;EAAQ;IAAAkE,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACW,CAAC;AAE3B,CAAC;AAACpE,GAAA,CA7MWF,YAAqD;AAAAuE,EAAA,GAArDvE,YAAqD;AAAA,IAAAuE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}