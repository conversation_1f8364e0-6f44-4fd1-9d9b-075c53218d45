{"ast": null, "code": "import { createClient } from '@supabase/supabase-js';\n\n// Supabase configuration\nconst supabaseUrl = process.env.REACT_APP_SUPABASE_URL || 'https://demo.supabase.co';\nconst supabaseAnonKey = process.env.REACT_APP_SUPABASE_ANON_KEY || 'demo-key';\nexport const supabase = createClient(supabaseUrl, supabaseAnonKey);\n\n// Database types\n\n// Helper functions for database operations\nexport const discService = {\n  // Ensure demo user exists\n  async ensureDemoUser() {\n    const demoUserId = '00000000-0000-0000-0000-000000000000';\n    try {\n      // Check if demo user exists\n      const {\n        data: existingUser\n      } = await supabase.from('profiles').select('id').eq('id', demoUserId).single();\n      if (!existingUser) {\n        // Create demo user\n        const {\n          error\n        } = await supabase.from('profiles').insert([{\n          id: demoUserId,\n          email: '<EMAIL>',\n          full_name: 'Demo User'\n        }]);\n        if (error) {\n          console.warn('Could not create demo user:', error);\n        }\n      }\n      return demoUserId;\n    } catch (error) {\n      console.warn('Demo user setup failed:', error);\n      return demoUserId;\n    }\n  },\n  // Create a new found disc report\n  async createFoundDisc(discData) {\n    try {\n      const {\n        data,\n        error\n      } = await supabase.from('found_discs').insert([{\n        ...discData,\n        status: 'active'\n      }]).select().single();\n      if (error) throw error;\n      return {\n        data,\n        error: null\n      };\n    } catch (error) {\n      console.error('Error creating found disc:', error);\n      return {\n        data: null,\n        error\n      };\n    }\n  },\n  // Get all active found discs (uses public view for role-based filtering)\n  async getFoundDiscs() {\n    try {\n      // Try the public view first, fallback to main table\n      let {\n        data,\n        error\n      } = await supabase.from('public_found_discs').select('*').order('created_at', {\n        ascending: false\n      });\n\n      // If public view doesn't exist or doesn't have image_urls, use main table\n      if (error || data && data.length > 0 && !data[0].hasOwnProperty('image_urls')) {\n        console.log('Using main table instead of view');\n        const result = await supabase.from('found_discs').select('*').eq('status', 'active').order('created_at', {\n          ascending: false\n        });\n        data = result.data;\n        error = result.error;\n      }\n      if (error) throw error;\n      return {\n        data,\n        error: null\n      };\n    } catch (error) {\n      console.error('Error fetching found discs:', error);\n      return {\n        data: null,\n        error\n      };\n    }\n  },\n  // Search found discs by criteria (uses public view for role-based filtering)\n  async searchFoundDiscs(searchCriteria) {\n    try {\n      // Try the public view first, fallback to main table\n      let query = supabase.from('public_found_discs').select('*');\n      if (searchCriteria.brand) {\n        query = query.ilike('brand', `%${searchCriteria.brand}%`);\n      }\n      if (searchCriteria.mold) {\n        query = query.ilike('mold', `%${searchCriteria.mold}%`);\n      }\n      if (searchCriteria.color) {\n        query = query.ilike('color', `%${searchCriteria.color}%`);\n      }\n      if (searchCriteria.discType) {\n        query = query.eq('disc_type', searchCriteria.discType);\n      }\n      if (searchCriteria.locationFound) {\n        query = query.ilike('location_found', `%${searchCriteria.locationFound}%`);\n      }\n      let {\n        data,\n        error\n      } = await query.order('created_at', {\n        ascending: false\n      });\n\n      // If public view doesn't exist or doesn't have image_urls, use main table\n      if (error || data && data.length > 0 && !data[0].hasOwnProperty('image_urls')) {\n        console.log('Using main table for search instead of view');\n        let mainQuery = supabase.from('found_discs').select('*').eq('status', 'active');\n        if (searchCriteria.brand) {\n          mainQuery = mainQuery.ilike('brand', `%${searchCriteria.brand}%`);\n        }\n        if (searchCriteria.mold) {\n          mainQuery = mainQuery.ilike('mold', `%${searchCriteria.mold}%`);\n        }\n        if (searchCriteria.color) {\n          mainQuery = mainQuery.ilike('color', `%${searchCriteria.color}%`);\n        }\n        if (searchCriteria.discType) {\n          mainQuery = mainQuery.eq('disc_type', searchCriteria.discType);\n        }\n        if (searchCriteria.locationFound) {\n          mainQuery = mainQuery.ilike('location_found', `%${searchCriteria.locationFound}%`);\n        }\n        const result = await mainQuery.order('created_at', {\n          ascending: false\n        });\n        data = result.data;\n        error = result.error;\n      }\n      if (error) throw error;\n      return {\n        data,\n        error: null\n      };\n    } catch (error) {\n      console.error('Error searching found discs:', error);\n      return {\n        data: null,\n        error\n      };\n    }\n  },\n  // Test connection to Supabase\n  async testConnection() {\n    try {\n      const {\n        data,\n        error\n      } = await supabase.from('found_discs').select('count').limit(1);\n      return {\n        connected: !error,\n        error\n      };\n    } catch (error) {\n      return {\n        connected: false,\n        error\n      };\n    }\n  },\n  // Update return status of a found disc (admin only)\n  async updateReturnStatus(discId, returnStatus, notes) {\n    try {\n      const {\n        data,\n        error\n      } = await supabase.rpc('update_disc_return_status', {\n        disc_id: discId,\n        new_status: returnStatus,\n        notes: notes || null\n      });\n      if (error) throw error;\n      return {\n        success: true,\n        error: null\n      };\n    } catch (error) {\n      console.error('Error updating return status:', error);\n      return {\n        success: false,\n        error\n      };\n    }\n  },\n  // Get all found discs for admin (includes all return statuses)\n  async getAdminFoundDiscs() {\n    try {\n      const {\n        data,\n        error\n      } = await supabase.from('admin_found_discs').select('*');\n      if (error) throw error;\n      return {\n        data,\n        error: null\n      };\n    } catch (error) {\n      console.error('Error fetching admin found discs:', error);\n      return {\n        data: null,\n        error\n      };\n    }\n  },\n  // Bulk Turn-In Functions\n\n  // Create a new bulk turn-in record (rakerdiver only)\n  async createBulkTurnin(turninData) {\n    try {\n      const {\n        data,\n        error\n      } = await supabase.rpc('create_bulk_turnin', {\n        p_location_collected: turninData.location_collected,\n        p_collection_date: turninData.collection_date,\n        p_disc_count: turninData.disc_count,\n        p_turnin_location: turninData.turnin_location,\n        p_turnin_date: turninData.turnin_date,\n        p_collection_time: turninData.collection_time || null,\n        p_turnin_time: turninData.turnin_time || null,\n        p_notes: turninData.notes || null\n      });\n      if (error) throw error;\n      return {\n        data,\n        error: null\n      };\n    } catch (error) {\n      console.error('Error creating bulk turn-in:', error);\n      return {\n        data: null,\n        error\n      };\n    }\n  },\n  // Get bulk turn-ins for current rakerdiver\n  async getRakerdiverTurnins() {\n    try {\n      const {\n        data,\n        error\n      } = await supabase.rpc('get_rakerdiver_turnins');\n      if (error) throw error;\n      return {\n        data,\n        error: null\n      };\n    } catch (error) {\n      console.error('Error fetching rakerdiver turn-ins:', error);\n      return {\n        data: null,\n        error\n      };\n    }\n  },\n  // Verify a bulk turn-in (admin only)\n  async verifyBulkTurnin(turninId, verificationNotes) {\n    try {\n      const {\n        data,\n        error\n      } = await supabase.rpc('verify_bulk_turnin', {\n        p_turnin_id: turninId,\n        p_verification_notes: verificationNotes || null\n      });\n      if (error) throw error;\n      return {\n        success: true,\n        error: null\n      };\n    } catch (error) {\n      console.error('Error verifying bulk turn-in:', error);\n      return {\n        success: false,\n        error\n      };\n    }\n  },\n  // Create a payment record (admin only)\n  async createBulkTurninPayment(paymentData) {\n    try {\n      const {\n        data,\n        error\n      } = await supabase.rpc('create_bulk_turnin_payment', {\n        p_bulk_turnin_id: paymentData.bulk_turnin_id,\n        p_amount: paymentData.amount,\n        p_payment_method: paymentData.payment_method || null,\n        p_payment_date: paymentData.payment_date || null,\n        p_payment_notes: paymentData.payment_notes || null\n      });\n      if (error) throw error;\n      return {\n        data,\n        error: null\n      };\n    } catch (error) {\n      console.error('Error creating payment:', error);\n      return {\n        data: null,\n        error\n      };\n    }\n  },\n  // Confirm payment receipt (rakerdiver only)\n  async confirmPaymentReceipt(paymentId) {\n    try {\n      const {\n        data,\n        error\n      } = await supabase.rpc('confirm_payment_receipt', {\n        p_payment_id: paymentId\n      });\n      if (error) throw error;\n      return {\n        success: true,\n        error: null\n      };\n    } catch (error) {\n      console.error('Error confirming payment:', error);\n      return {\n        success: false,\n        error\n      };\n    }\n  },\n  // Get all bulk turn-ins for admin\n  async getAdminBulkTurnins() {\n    try {\n      const {\n        data,\n        error\n      } = await supabase.from('admin_bulk_turnins').select('*');\n      if (error) throw error;\n      return {\n        data,\n        error: null\n      };\n    } catch (error) {\n      console.error('Error fetching admin bulk turn-ins:', error);\n      return {\n        data: null,\n        error\n      };\n    }\n  },\n  // Get payments for a specific bulk turn-in\n  async getBulkTurninPayments(turninId) {\n    try {\n      const {\n        data,\n        error\n      } = await supabase.from('bulk_turnin_payments').select('*').eq('bulk_turnin_id', turninId).order('created_at', {\n        ascending: false\n      });\n      if (error) throw error;\n      return {\n        data,\n        error: null\n      };\n    } catch (error) {\n      console.error('Error fetching payments:', error);\n      return {\n        data: null,\n        error\n      };\n    }\n  }\n};\n\n// Image upload service\nexport const imageService = {\n  // Upload multiple images to Supabase storage\n  async uploadImages(files, userId) {\n    try {\n      console.log(`Starting upload of ${files.length} files for user ${userId}`);\n\n      // Check if user is authenticated\n      const {\n        data: {\n          user\n        }\n      } = await supabase.auth.getUser();\n      if (!user) {\n        throw new Error('User not authenticated');\n      }\n      console.log('User authenticated:', user.id);\n      const uploadPromises = files.map(async (file, index) => {\n        // Generate unique filename\n        const fileExt = file.name.split('.').pop();\n        const fileName = `${userId}/${Date.now()}-${index}.${fileExt}`;\n        console.log(`Uploading file ${index + 1}/${files.length}: ${fileName}`);\n        const {\n          data,\n          error\n        } = await supabase.storage.from('disc-images').upload(fileName, file, {\n          cacheControl: '3600',\n          upsert: false\n        });\n        if (error) {\n          console.error(`Upload error for ${fileName}:`, error);\n          throw error;\n        }\n        console.log(`Upload successful for ${fileName}:`, data);\n\n        // Get public URL\n        const {\n          data: urlData\n        } = supabase.storage.from('disc-images').getPublicUrl(fileName);\n        console.log(`Public URL for ${fileName}:`, urlData.publicUrl);\n        return urlData.publicUrl;\n      });\n      const urls = await Promise.all(uploadPromises);\n      console.log('All uploads completed:', urls);\n      return {\n        urls,\n        error: null\n      };\n    } catch (error) {\n      console.error('Error uploading images:', error);\n      return {\n        urls: [],\n        error\n      };\n    }\n  },\n  // Delete images from Supabase storage\n  async deleteImages(imageUrls) {\n    try {\n      // Extract file paths from URLs\n      const filePaths = imageUrls.map(url => {\n        const urlParts = url.split('/');\n        const bucketIndex = urlParts.findIndex(part => part === 'disc-images');\n        if (bucketIndex !== -1 && bucketIndex < urlParts.length - 1) {\n          return urlParts.slice(bucketIndex + 1).join('/');\n        }\n        return null;\n      }).filter(Boolean);\n      if (filePaths.length === 0) {\n        return {\n          success: true,\n          error: null\n        };\n      }\n      const {\n        error\n      } = await supabase.storage.from('disc-images').remove(filePaths);\n      return {\n        success: !error,\n        error\n      };\n    } catch (error) {\n      console.error('Error deleting images:', error);\n      return {\n        success: false,\n        error\n      };\n    }\n  },\n  // Get optimized image URL (for future use with Supabase image transformations)\n  getOptimizedImageUrl(originalUrl, width, height) {\n    // For now, return original URL\n    // In the future, you can add Supabase image transformation parameters\n    return originalUrl;\n  },\n  // Validate image file\n  validateImageFile(file, maxSizeMB = 10) {\n    // Check file type\n    if (!file.type.startsWith('image/')) {\n      return {\n        valid: false,\n        error: 'File must be an image'\n      };\n    }\n\n    // Check file size\n    const maxSizeBytes = maxSizeMB * 1024 * 1024;\n    if (file.size > maxSizeBytes) {\n      return {\n        valid: false,\n        error: `Image size must be less than ${maxSizeMB}MB`\n      };\n    }\n\n    // Check supported formats\n    const supportedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];\n    if (!supportedTypes.includes(file.type)) {\n      return {\n        valid: false,\n        error: 'Supported formats: JPEG, PNG, WebP'\n      };\n    }\n    return {\n      valid: true\n    };\n  }\n};", "map": {"version": 3, "names": ["createClient", "supabaseUrl", "process", "env", "REACT_APP_SUPABASE_URL", "supabaseAnonKey", "REACT_APP_SUPABASE_ANON_KEY", "supabase", "discService", "ensureDemoUser", "demoUserId", "data", "existingUser", "from", "select", "eq", "single", "error", "insert", "id", "email", "full_name", "console", "warn", "createFoundDisc", "discData", "status", "getFoundDiscs", "order", "ascending", "length", "hasOwnProperty", "log", "result", "searchFoundDiscs", "searchCriteria", "query", "brand", "ilike", "mold", "color", "discType", "locationFound", "<PERSON><PERSON><PERSON><PERSON>", "testConnection", "limit", "connected", "updateReturnStatus", "discId", "returnStatus", "notes", "rpc", "disc_id", "new_status", "success", "getAdminFoundDiscs", "createBulkTurnin", "turninData", "p_location_collected", "location_collected", "p_collection_date", "collection_date", "p_disc_count", "disc_count", "p_turnin_location", "turnin_location", "p_turnin_date", "turnin_date", "p_collection_time", "collection_time", "p_turnin_time", "turnin_time", "p_notes", "getRakerdiverTurnins", "verifyBulkTurnin", "turninId", "verificationNotes", "p_turnin_id", "p_verification_notes", "createBulkTurninPayment", "paymentData", "p_bulk_turnin_id", "bulk_turnin_id", "p_amount", "amount", "p_payment_method", "payment_method", "p_payment_date", "payment_date", "p_payment_notes", "payment_notes", "confirmPaymentReceipt", "paymentId", "p_payment_id", "getAdminBulkTurnins", "getBulkTurninPayments", "imageService", "uploadImages", "files", "userId", "user", "auth", "getUser", "Error", "uploadPromises", "map", "file", "index", "fileExt", "name", "split", "pop", "fileName", "Date", "now", "storage", "upload", "cacheControl", "upsert", "urlData", "getPublicUrl", "publicUrl", "urls", "Promise", "all", "deleteImages", "imageUrls", "filePaths", "url", "urlParts", "bucketIndex", "findIndex", "part", "slice", "join", "filter", "Boolean", "remove", "getOptimizedImageUrl", "originalUrl", "width", "height", "validateImageFile", "maxSizeMB", "type", "startsWith", "valid", "maxSizeBytes", "size", "supportedTypes", "includes"], "sources": ["C:/Users/<USER>/lostandfound/discfinder-app/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js'\n\n// Supabase configuration\nconst supabaseUrl = process.env.REACT_APP_SUPABASE_URL || 'https://demo.supabase.co'\nconst supabaseAnonKey = process.env.REACT_APP_SUPABASE_ANON_KEY || 'demo-key'\n\nexport const supabase = createClient(supabaseUrl, supabaseAnonKey)\n\n// Database types\nexport type UserRole = 'guest' | 'user' | 'admin' | 'rakerdiver';\n\nexport interface Profile {\n  id: string\n  email: string\n  full_name?: string\n  phone?: string\n  role?: UserRole\n  created_at: string\n  updated_at: string\n}\n\nexport type ReturnStatus = 'Found' | 'Returned to Owner' | 'Donated' | 'Sold' | 'Trashed';\n\nexport interface FoundDisc {\n  id: string\n  finder_id: string\n  brand: string\n  mold?: string\n  disc_type?: string\n  color: string\n  weight?: number\n  condition?: string\n  plastic_type?: string\n  stamp_text?: string\n  phone_number?: string\n  name_on_disc?: string\n  location_found: string\n  location_coordinates?: { x: number; y: number }\n  found_date: string\n  description?: string\n  image_urls?: string[]\n  status: 'active' | 'claimed' | 'expired' | 'spam'\n  return_status?: ReturnStatus\n  returned_at?: string\n  returned_notes?: string\n  created_at: string\n  updated_at: string\n}\n\nexport interface LostDisc {\n  id: string\n  owner_id: string\n  brand: string\n  model?: string\n  disc_type?: 'driver' | 'fairway_driver' | 'midrange' | 'putter' | 'approach' | 'distance_driver'\n  color: string\n  weight?: number\n  plastic_type?: string\n  stamp_text?: string\n  location_lost: string\n  location_coordinates?: { x: number; y: number }\n  lost_date: string\n  description?: string\n  reward_offered?: number\n  contact_preference: string\n  status: 'active' | 'claimed' | 'expired' | 'spam'\n  created_at: string\n  updated_at: string\n}\n\nexport interface BulkTurnin {\n  id: string\n  rakerdiver_id: string\n  location_collected: string\n  collection_date: string\n  collection_time?: string\n  disc_count: number\n  turnin_location: string\n  turnin_date: string\n  turnin_time?: string\n  notes?: string\n  admin_verified: boolean\n  verified_by?: string\n  verified_at?: string\n  verification_notes?: string\n  created_at: string\n  updated_at: string\n  total_payments?: number\n  confirmed_payments?: number\n}\n\nexport interface BulkTurninPayment {\n  id: string\n  bulk_turnin_id: string\n  amount: number\n  payment_method?: string\n  payment_date?: string\n  payment_notes?: string\n  created_by: string\n  rakerdiver_confirmed: boolean\n  confirmed_at?: string\n  created_at: string\n  updated_at: string\n}\n\nexport interface AdminBulkTurnin extends BulkTurnin {\n  rakerdiver_name?: string\n  rakerdiver_email?: string\n  verified_by_name?: string\n  payment_count?: number\n}\n\nexport interface DiscMatch {\n  id: string\n  found_disc_id: string\n  lost_disc_id: string\n  match_score: number\n  status: 'potential' | 'confirmed' | 'rejected'\n  finder_contacted_at?: string\n  owner_contacted_at?: string\n  created_at: string\n  updated_at: string\n  found_disc?: FoundDisc\n  lost_disc?: LostDisc\n}\n\n// Helper functions for database operations\nexport const discService = {\n  // Ensure demo user exists\n  async ensureDemoUser() {\n    const demoUserId = '00000000-0000-0000-0000-000000000000';\n    try {\n      // Check if demo user exists\n      const { data: existingUser } = await supabase\n        .from('profiles')\n        .select('id')\n        .eq('id', demoUserId)\n        .single();\n\n      if (!existingUser) {\n        // Create demo user\n        const { error } = await supabase\n          .from('profiles')\n          .insert([{\n            id: demoUserId,\n            email: '<EMAIL>',\n            full_name: 'Demo User'\n          }]);\n\n        if (error) {\n          console.warn('Could not create demo user:', error);\n        }\n      }\n      return demoUserId;\n    } catch (error) {\n      console.warn('Demo user setup failed:', error);\n      return demoUserId;\n    }\n  },\n\n  // Create a new found disc report\n  async createFoundDisc(discData: Omit<FoundDisc, 'id' | 'created_at' | 'updated_at' | 'status'>) {\n    try {\n      const { data, error } = await supabase\n        .from('found_discs')\n        .insert([{\n          ...discData,\n          status: 'active'\n        }])\n        .select()\n        .single()\n\n      if (error) throw error\n      return { data, error: null }\n    } catch (error) {\n      console.error('Error creating found disc:', error)\n      return { data: null, error }\n    }\n  },\n\n  // Get all active found discs (uses public view for role-based filtering)\n  async getFoundDiscs() {\n    try {\n      // Try the public view first, fallback to main table\n      let { data, error } = await supabase\n        .from('public_found_discs')\n        .select('*')\n        .order('created_at', { ascending: false })\n\n      // If public view doesn't exist or doesn't have image_urls, use main table\n      if (error || (data && data.length > 0 && !data[0].hasOwnProperty('image_urls'))) {\n        console.log('Using main table instead of view');\n        const result = await supabase\n          .from('found_discs')\n          .select('*')\n          .eq('status', 'active')\n          .order('created_at', { ascending: false });\n\n        data = result.data;\n        error = result.error;\n      }\n\n      if (error) throw error\n      return { data, error: null }\n    } catch (error) {\n      console.error('Error fetching found discs:', error)\n      return { data: null, error }\n    }\n  },\n\n  // Search found discs by criteria (uses public view for role-based filtering)\n  async searchFoundDiscs(searchCriteria: {\n    brand?: string\n    mold?: string\n    color?: string\n    discType?: string\n    locationFound?: string\n  }) {\n    try {\n      // Try the public view first, fallback to main table\n      let query = supabase\n        .from('public_found_discs')\n        .select('*')\n\n      if (searchCriteria.brand) {\n        query = query.ilike('brand', `%${searchCriteria.brand}%`)\n      }\n      if (searchCriteria.mold) {\n        query = query.ilike('mold', `%${searchCriteria.mold}%`)\n      }\n      if (searchCriteria.color) {\n        query = query.ilike('color', `%${searchCriteria.color}%`)\n      }\n      if (searchCriteria.discType) {\n        query = query.eq('disc_type', searchCriteria.discType)\n      }\n      if (searchCriteria.locationFound) {\n        query = query.ilike('location_found', `%${searchCriteria.locationFound}%`)\n      }\n\n      let { data, error } = await query.order('created_at', { ascending: false })\n\n      // If public view doesn't exist or doesn't have image_urls, use main table\n      if (error || (data && data.length > 0 && !data[0].hasOwnProperty('image_urls'))) {\n        console.log('Using main table for search instead of view');\n        let mainQuery = supabase\n          .from('found_discs')\n          .select('*')\n          .eq('status', 'active')\n\n        if (searchCriteria.brand) {\n          mainQuery = mainQuery.ilike('brand', `%${searchCriteria.brand}%`)\n        }\n        if (searchCriteria.mold) {\n          mainQuery = mainQuery.ilike('mold', `%${searchCriteria.mold}%`)\n        }\n        if (searchCriteria.color) {\n          mainQuery = mainQuery.ilike('color', `%${searchCriteria.color}%`)\n        }\n        if (searchCriteria.discType) {\n          mainQuery = mainQuery.eq('disc_type', searchCriteria.discType)\n        }\n        if (searchCriteria.locationFound) {\n          mainQuery = mainQuery.ilike('location_found', `%${searchCriteria.locationFound}%`)\n        }\n\n        const result = await mainQuery.order('created_at', { ascending: false });\n        data = result.data;\n        error = result.error;\n      }\n\n      if (error) throw error\n      return { data, error: null }\n    } catch (error) {\n      console.error('Error searching found discs:', error)\n      return { data: null, error }\n    }\n  },\n\n  // Test connection to Supabase\n  async testConnection() {\n    try {\n      const { data, error } = await supabase\n        .from('found_discs')\n        .select('count')\n        .limit(1)\n\n      return { connected: !error, error }\n    } catch (error) {\n      return { connected: false, error }\n    }\n  },\n\n  // Update return status of a found disc (admin only)\n  async updateReturnStatus(discId: string, returnStatus: ReturnStatus, notes?: string) {\n    try {\n      const { data, error } = await supabase.rpc('update_disc_return_status', {\n        disc_id: discId,\n        new_status: returnStatus,\n        notes: notes || null\n      });\n\n      if (error) throw error;\n      return { success: true, error: null };\n    } catch (error) {\n      console.error('Error updating return status:', error);\n      return { success: false, error };\n    }\n  },\n\n  // Get all found discs for admin (includes all return statuses)\n  async getAdminFoundDiscs() {\n    try {\n      const { data, error } = await supabase\n        .from('admin_found_discs')\n        .select('*');\n\n      if (error) throw error;\n      return { data, error: null };\n    } catch (error) {\n      console.error('Error fetching admin found discs:', error);\n      return { data: null, error };\n    }\n  },\n\n  // Bulk Turn-In Functions\n\n  // Create a new bulk turn-in record (rakerdiver only)\n  async createBulkTurnin(turninData: {\n    location_collected: string;\n    collection_date: string;\n    collection_time?: string;\n    disc_count: number;\n    turnin_location: string;\n    turnin_date: string;\n    turnin_time?: string;\n    notes?: string;\n  }) {\n    try {\n      const { data, error } = await supabase.rpc('create_bulk_turnin', {\n        p_location_collected: turninData.location_collected,\n        p_collection_date: turninData.collection_date,\n        p_disc_count: turninData.disc_count,\n        p_turnin_location: turninData.turnin_location,\n        p_turnin_date: turninData.turnin_date,\n        p_collection_time: turninData.collection_time || null,\n        p_turnin_time: turninData.turnin_time || null,\n        p_notes: turninData.notes || null\n      });\n\n      if (error) throw error;\n      return { data, error: null };\n    } catch (error) {\n      console.error('Error creating bulk turn-in:', error);\n      return { data: null, error };\n    }\n  },\n\n  // Get bulk turn-ins for current rakerdiver\n  async getRakerdiverTurnins() {\n    try {\n      const { data, error } = await supabase.rpc('get_rakerdiver_turnins');\n\n      if (error) throw error;\n      return { data, error: null };\n    } catch (error) {\n      console.error('Error fetching rakerdiver turn-ins:', error);\n      return { data: null, error };\n    }\n  },\n\n  // Verify a bulk turn-in (admin only)\n  async verifyBulkTurnin(turninId: string, verificationNotes?: string) {\n    try {\n      const { data, error } = await supabase.rpc('verify_bulk_turnin', {\n        p_turnin_id: turninId,\n        p_verification_notes: verificationNotes || null\n      });\n\n      if (error) throw error;\n      return { success: true, error: null };\n    } catch (error) {\n      console.error('Error verifying bulk turn-in:', error);\n      return { success: false, error };\n    }\n  },\n\n  // Create a payment record (admin only)\n  async createBulkTurninPayment(paymentData: {\n    bulk_turnin_id: string;\n    amount: number;\n    payment_method?: string;\n    payment_date?: string;\n    payment_notes?: string;\n  }) {\n    try {\n      const { data, error } = await supabase.rpc('create_bulk_turnin_payment', {\n        p_bulk_turnin_id: paymentData.bulk_turnin_id,\n        p_amount: paymentData.amount,\n        p_payment_method: paymentData.payment_method || null,\n        p_payment_date: paymentData.payment_date || null,\n        p_payment_notes: paymentData.payment_notes || null\n      });\n\n      if (error) throw error;\n      return { data, error: null };\n    } catch (error) {\n      console.error('Error creating payment:', error);\n      return { data: null, error };\n    }\n  },\n\n  // Confirm payment receipt (rakerdiver only)\n  async confirmPaymentReceipt(paymentId: string) {\n    try {\n      const { data, error } = await supabase.rpc('confirm_payment_receipt', {\n        p_payment_id: paymentId\n      });\n\n      if (error) throw error;\n      return { success: true, error: null };\n    } catch (error) {\n      console.error('Error confirming payment:', error);\n      return { success: false, error };\n    }\n  },\n\n  // Get all bulk turn-ins for admin\n  async getAdminBulkTurnins() {\n    try {\n      const { data, error } = await supabase\n        .from('admin_bulk_turnins')\n        .select('*');\n\n      if (error) throw error;\n      return { data, error: null };\n    } catch (error) {\n      console.error('Error fetching admin bulk turn-ins:', error);\n      return { data: null, error };\n    }\n  },\n\n  // Get payments for a specific bulk turn-in\n  async getBulkTurninPayments(turninId: string) {\n    try {\n      const { data, error } = await supabase\n        .from('bulk_turnin_payments')\n        .select('*')\n        .eq('bulk_turnin_id', turninId)\n        .order('created_at', { ascending: false });\n\n      if (error) throw error;\n      return { data, error: null };\n    } catch (error) {\n      console.error('Error fetching payments:', error);\n      return { data: null, error };\n    }\n  }\n}\n\n// Image upload service\nexport const imageService = {\n  // Upload multiple images to Supabase storage\n  async uploadImages(files: File[], userId: string): Promise<{ urls: string[], error: any }> {\n    try {\n      console.log(`Starting upload of ${files.length} files for user ${userId}`);\n\n      // Check if user is authenticated\n      const { data: { user } } = await supabase.auth.getUser();\n      if (!user) {\n        throw new Error('User not authenticated');\n      }\n\n      console.log('User authenticated:', user.id);\n\n      const uploadPromises = files.map(async (file, index) => {\n        // Generate unique filename\n        const fileExt = file.name.split('.').pop();\n        const fileName = `${userId}/${Date.now()}-${index}.${fileExt}`;\n\n        console.log(`Uploading file ${index + 1}/${files.length}: ${fileName}`);\n\n        const { data, error } = await supabase.storage\n          .from('disc-images')\n          .upload(fileName, file, {\n            cacheControl: '3600',\n            upsert: false\n          });\n\n        if (error) {\n          console.error(`Upload error for ${fileName}:`, error);\n          throw error;\n        }\n\n        console.log(`Upload successful for ${fileName}:`, data);\n\n        // Get public URL\n        const { data: urlData } = supabase.storage\n          .from('disc-images')\n          .getPublicUrl(fileName);\n\n        console.log(`Public URL for ${fileName}:`, urlData.publicUrl);\n        return urlData.publicUrl;\n      });\n\n      const urls = await Promise.all(uploadPromises);\n      console.log('All uploads completed:', urls);\n      return { urls, error: null };\n    } catch (error) {\n      console.error('Error uploading images:', error);\n      return { urls: [], error };\n    }\n  },\n\n  // Delete images from Supabase storage\n  async deleteImages(imageUrls: string[]): Promise<{ success: boolean, error: any }> {\n    try {\n      // Extract file paths from URLs\n      const filePaths = imageUrls.map(url => {\n        const urlParts = url.split('/');\n        const bucketIndex = urlParts.findIndex(part => part === 'disc-images');\n        if (bucketIndex !== -1 && bucketIndex < urlParts.length - 1) {\n          return urlParts.slice(bucketIndex + 1).join('/');\n        }\n        return null;\n      }).filter(Boolean) as string[];\n\n      if (filePaths.length === 0) {\n        return { success: true, error: null };\n      }\n\n      const { error } = await supabase.storage\n        .from('disc-images')\n        .remove(filePaths);\n\n      return { success: !error, error };\n    } catch (error) {\n      console.error('Error deleting images:', error);\n      return { success: false, error };\n    }\n  },\n\n  // Get optimized image URL (for future use with Supabase image transformations)\n  getOptimizedImageUrl(originalUrl: string, width?: number, height?: number): string {\n    // For now, return original URL\n    // In the future, you can add Supabase image transformation parameters\n    return originalUrl;\n  },\n\n  // Validate image file\n  validateImageFile(file: File, maxSizeMB: number = 10): { valid: boolean, error?: string } {\n    // Check file type\n    if (!file.type.startsWith('image/')) {\n      return { valid: false, error: 'File must be an image' };\n    }\n\n    // Check file size\n    const maxSizeBytes = maxSizeMB * 1024 * 1024;\n    if (file.size > maxSizeBytes) {\n      return { valid: false, error: `Image size must be less than ${maxSizeMB}MB` };\n    }\n\n    // Check supported formats\n    const supportedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];\n    if (!supportedTypes.includes(file.type)) {\n      return { valid: false, error: 'Supported formats: JPEG, PNG, WebP' };\n    }\n\n    return { valid: true };\n  }\n}\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,uBAAuB;;AAEpD;AACA,MAAMC,WAAW,GAAGC,OAAO,CAACC,GAAG,CAACC,sBAAsB,IAAI,0BAA0B;AACpF,MAAMC,eAAe,GAAGH,OAAO,CAACC,GAAG,CAACG,2BAA2B,IAAI,UAAU;AAE7E,OAAO,MAAMC,QAAQ,GAAGP,YAAY,CAACC,WAAW,EAAEI,eAAe,CAAC;;AAElE;;AAsHA;AACA,OAAO,MAAMG,WAAW,GAAG;EACzB;EACA,MAAMC,cAAcA,CAAA,EAAG;IACrB,MAAMC,UAAU,GAAG,sCAAsC;IACzD,IAAI;MACF;MACA,MAAM;QAAEC,IAAI,EAAEC;MAAa,CAAC,GAAG,MAAML,QAAQ,CAC1CM,IAAI,CAAC,UAAU,CAAC,CAChBC,MAAM,CAAC,IAAI,CAAC,CACZC,EAAE,CAAC,IAAI,EAAEL,UAAU,CAAC,CACpBM,MAAM,CAAC,CAAC;MAEX,IAAI,CAACJ,YAAY,EAAE;QACjB;QACA,MAAM;UAAEK;QAAM,CAAC,GAAG,MAAMV,QAAQ,CAC7BM,IAAI,CAAC,UAAU,CAAC,CAChBK,MAAM,CAAC,CAAC;UACPC,EAAE,EAAET,UAAU;UACdU,KAAK,EAAE,qBAAqB;UAC5BC,SAAS,EAAE;QACb,CAAC,CAAC,CAAC;QAEL,IAAIJ,KAAK,EAAE;UACTK,OAAO,CAACC,IAAI,CAAC,6BAA6B,EAAEN,KAAK,CAAC;QACpD;MACF;MACA,OAAOP,UAAU;IACnB,CAAC,CAAC,OAAOO,KAAK,EAAE;MACdK,OAAO,CAACC,IAAI,CAAC,yBAAyB,EAAEN,KAAK,CAAC;MAC9C,OAAOP,UAAU;IACnB;EACF,CAAC;EAED;EACA,MAAMc,eAAeA,CAACC,QAAwE,EAAE;IAC9F,IAAI;MACF,MAAM;QAAEd,IAAI;QAAEM;MAAM,CAAC,GAAG,MAAMV,QAAQ,CACnCM,IAAI,CAAC,aAAa,CAAC,CACnBK,MAAM,CAAC,CAAC;QACP,GAAGO,QAAQ;QACXC,MAAM,EAAE;MACV,CAAC,CAAC,CAAC,CACFZ,MAAM,CAAC,CAAC,CACRE,MAAM,CAAC,CAAC;MAEX,IAAIC,KAAK,EAAE,MAAMA,KAAK;MACtB,OAAO;QAAEN,IAAI;QAAEM,KAAK,EAAE;MAAK,CAAC;IAC9B,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdK,OAAO,CAACL,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClD,OAAO;QAAEN,IAAI,EAAE,IAAI;QAAEM;MAAM,CAAC;IAC9B;EACF,CAAC;EAED;EACA,MAAMU,aAAaA,CAAA,EAAG;IACpB,IAAI;MACF;MACA,IAAI;QAAEhB,IAAI;QAAEM;MAAM,CAAC,GAAG,MAAMV,QAAQ,CACjCM,IAAI,CAAC,oBAAoB,CAAC,CAC1BC,MAAM,CAAC,GAAG,CAAC,CACXc,KAAK,CAAC,YAAY,EAAE;QAAEC,SAAS,EAAE;MAAM,CAAC,CAAC;;MAE5C;MACA,IAAIZ,KAAK,IAAKN,IAAI,IAAIA,IAAI,CAACmB,MAAM,GAAG,CAAC,IAAI,CAACnB,IAAI,CAAC,CAAC,CAAC,CAACoB,cAAc,CAAC,YAAY,CAAE,EAAE;QAC/ET,OAAO,CAACU,GAAG,CAAC,kCAAkC,CAAC;QAC/C,MAAMC,MAAM,GAAG,MAAM1B,QAAQ,CAC1BM,IAAI,CAAC,aAAa,CAAC,CACnBC,MAAM,CAAC,GAAG,CAAC,CACXC,EAAE,CAAC,QAAQ,EAAE,QAAQ,CAAC,CACtBa,KAAK,CAAC,YAAY,EAAE;UAAEC,SAAS,EAAE;QAAM,CAAC,CAAC;QAE5ClB,IAAI,GAAGsB,MAAM,CAACtB,IAAI;QAClBM,KAAK,GAAGgB,MAAM,CAAChB,KAAK;MACtB;MAEA,IAAIA,KAAK,EAAE,MAAMA,KAAK;MACtB,OAAO;QAAEN,IAAI;QAAEM,KAAK,EAAE;MAAK,CAAC;IAC9B,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdK,OAAO,CAACL,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnD,OAAO;QAAEN,IAAI,EAAE,IAAI;QAAEM;MAAM,CAAC;IAC9B;EACF,CAAC;EAED;EACA,MAAMiB,gBAAgBA,CAACC,cAMtB,EAAE;IACD,IAAI;MACF;MACA,IAAIC,KAAK,GAAG7B,QAAQ,CACjBM,IAAI,CAAC,oBAAoB,CAAC,CAC1BC,MAAM,CAAC,GAAG,CAAC;MAEd,IAAIqB,cAAc,CAACE,KAAK,EAAE;QACxBD,KAAK,GAAGA,KAAK,CAACE,KAAK,CAAC,OAAO,EAAE,IAAIH,cAAc,CAACE,KAAK,GAAG,CAAC;MAC3D;MACA,IAAIF,cAAc,CAACI,IAAI,EAAE;QACvBH,KAAK,GAAGA,KAAK,CAACE,KAAK,CAAC,MAAM,EAAE,IAAIH,cAAc,CAACI,IAAI,GAAG,CAAC;MACzD;MACA,IAAIJ,cAAc,CAACK,KAAK,EAAE;QACxBJ,KAAK,GAAGA,KAAK,CAACE,KAAK,CAAC,OAAO,EAAE,IAAIH,cAAc,CAACK,KAAK,GAAG,CAAC;MAC3D;MACA,IAAIL,cAAc,CAACM,QAAQ,EAAE;QAC3BL,KAAK,GAAGA,KAAK,CAACrB,EAAE,CAAC,WAAW,EAAEoB,cAAc,CAACM,QAAQ,CAAC;MACxD;MACA,IAAIN,cAAc,CAACO,aAAa,EAAE;QAChCN,KAAK,GAAGA,KAAK,CAACE,KAAK,CAAC,gBAAgB,EAAE,IAAIH,cAAc,CAACO,aAAa,GAAG,CAAC;MAC5E;MAEA,IAAI;QAAE/B,IAAI;QAAEM;MAAM,CAAC,GAAG,MAAMmB,KAAK,CAACR,KAAK,CAAC,YAAY,EAAE;QAAEC,SAAS,EAAE;MAAM,CAAC,CAAC;;MAE3E;MACA,IAAIZ,KAAK,IAAKN,IAAI,IAAIA,IAAI,CAACmB,MAAM,GAAG,CAAC,IAAI,CAACnB,IAAI,CAAC,CAAC,CAAC,CAACoB,cAAc,CAAC,YAAY,CAAE,EAAE;QAC/ET,OAAO,CAACU,GAAG,CAAC,6CAA6C,CAAC;QAC1D,IAAIW,SAAS,GAAGpC,QAAQ,CACrBM,IAAI,CAAC,aAAa,CAAC,CACnBC,MAAM,CAAC,GAAG,CAAC,CACXC,EAAE,CAAC,QAAQ,EAAE,QAAQ,CAAC;QAEzB,IAAIoB,cAAc,CAACE,KAAK,EAAE;UACxBM,SAAS,GAAGA,SAAS,CAACL,KAAK,CAAC,OAAO,EAAE,IAAIH,cAAc,CAACE,KAAK,GAAG,CAAC;QACnE;QACA,IAAIF,cAAc,CAACI,IAAI,EAAE;UACvBI,SAAS,GAAGA,SAAS,CAACL,KAAK,CAAC,MAAM,EAAE,IAAIH,cAAc,CAACI,IAAI,GAAG,CAAC;QACjE;QACA,IAAIJ,cAAc,CAACK,KAAK,EAAE;UACxBG,SAAS,GAAGA,SAAS,CAACL,KAAK,CAAC,OAAO,EAAE,IAAIH,cAAc,CAACK,KAAK,GAAG,CAAC;QACnE;QACA,IAAIL,cAAc,CAACM,QAAQ,EAAE;UAC3BE,SAAS,GAAGA,SAAS,CAAC5B,EAAE,CAAC,WAAW,EAAEoB,cAAc,CAACM,QAAQ,CAAC;QAChE;QACA,IAAIN,cAAc,CAACO,aAAa,EAAE;UAChCC,SAAS,GAAGA,SAAS,CAACL,KAAK,CAAC,gBAAgB,EAAE,IAAIH,cAAc,CAACO,aAAa,GAAG,CAAC;QACpF;QAEA,MAAMT,MAAM,GAAG,MAAMU,SAAS,CAACf,KAAK,CAAC,YAAY,EAAE;UAAEC,SAAS,EAAE;QAAM,CAAC,CAAC;QACxElB,IAAI,GAAGsB,MAAM,CAACtB,IAAI;QAClBM,KAAK,GAAGgB,MAAM,CAAChB,KAAK;MACtB;MAEA,IAAIA,KAAK,EAAE,MAAMA,KAAK;MACtB,OAAO;QAAEN,IAAI;QAAEM,KAAK,EAAE;MAAK,CAAC;IAC9B,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdK,OAAO,CAACL,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpD,OAAO;QAAEN,IAAI,EAAE,IAAI;QAAEM;MAAM,CAAC;IAC9B;EACF,CAAC;EAED;EACA,MAAM2B,cAAcA,CAAA,EAAG;IACrB,IAAI;MACF,MAAM;QAAEjC,IAAI;QAAEM;MAAM,CAAC,GAAG,MAAMV,QAAQ,CACnCM,IAAI,CAAC,aAAa,CAAC,CACnBC,MAAM,CAAC,OAAO,CAAC,CACf+B,KAAK,CAAC,CAAC,CAAC;MAEX,OAAO;QAAEC,SAAS,EAAE,CAAC7B,KAAK;QAAEA;MAAM,CAAC;IACrC,CAAC,CAAC,OAAOA,KAAK,EAAE;MACd,OAAO;QAAE6B,SAAS,EAAE,KAAK;QAAE7B;MAAM,CAAC;IACpC;EACF,CAAC;EAED;EACA,MAAM8B,kBAAkBA,CAACC,MAAc,EAAEC,YAA0B,EAAEC,KAAc,EAAE;IACnF,IAAI;MACF,MAAM;QAAEvC,IAAI;QAAEM;MAAM,CAAC,GAAG,MAAMV,QAAQ,CAAC4C,GAAG,CAAC,2BAA2B,EAAE;QACtEC,OAAO,EAAEJ,MAAM;QACfK,UAAU,EAAEJ,YAAY;QACxBC,KAAK,EAAEA,KAAK,IAAI;MAClB,CAAC,CAAC;MAEF,IAAIjC,KAAK,EAAE,MAAMA,KAAK;MACtB,OAAO;QAAEqC,OAAO,EAAE,IAAI;QAAErC,KAAK,EAAE;MAAK,CAAC;IACvC,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdK,OAAO,CAACL,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrD,OAAO;QAAEqC,OAAO,EAAE,KAAK;QAAErC;MAAM,CAAC;IAClC;EACF,CAAC;EAED;EACA,MAAMsC,kBAAkBA,CAAA,EAAG;IACzB,IAAI;MACF,MAAM;QAAE5C,IAAI;QAAEM;MAAM,CAAC,GAAG,MAAMV,QAAQ,CACnCM,IAAI,CAAC,mBAAmB,CAAC,CACzBC,MAAM,CAAC,GAAG,CAAC;MAEd,IAAIG,KAAK,EAAE,MAAMA,KAAK;MACtB,OAAO;QAAEN,IAAI;QAAEM,KAAK,EAAE;MAAK,CAAC;IAC9B,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdK,OAAO,CAACL,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;MACzD,OAAO;QAAEN,IAAI,EAAE,IAAI;QAAEM;MAAM,CAAC;IAC9B;EACF,CAAC;EAED;;EAEA;EACA,MAAMuC,gBAAgBA,CAACC,UAStB,EAAE;IACD,IAAI;MACF,MAAM;QAAE9C,IAAI;QAAEM;MAAM,CAAC,GAAG,MAAMV,QAAQ,CAAC4C,GAAG,CAAC,oBAAoB,EAAE;QAC/DO,oBAAoB,EAAED,UAAU,CAACE,kBAAkB;QACnDC,iBAAiB,EAAEH,UAAU,CAACI,eAAe;QAC7CC,YAAY,EAAEL,UAAU,CAACM,UAAU;QACnCC,iBAAiB,EAAEP,UAAU,CAACQ,eAAe;QAC7CC,aAAa,EAAET,UAAU,CAACU,WAAW;QACrCC,iBAAiB,EAAEX,UAAU,CAACY,eAAe,IAAI,IAAI;QACrDC,aAAa,EAAEb,UAAU,CAACc,WAAW,IAAI,IAAI;QAC7CC,OAAO,EAAEf,UAAU,CAACP,KAAK,IAAI;MAC/B,CAAC,CAAC;MAEF,IAAIjC,KAAK,EAAE,MAAMA,KAAK;MACtB,OAAO;QAAEN,IAAI;QAAEM,KAAK,EAAE;MAAK,CAAC;IAC9B,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdK,OAAO,CAACL,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpD,OAAO;QAAEN,IAAI,EAAE,IAAI;QAAEM;MAAM,CAAC;IAC9B;EACF,CAAC;EAED;EACA,MAAMwD,oBAAoBA,CAAA,EAAG;IAC3B,IAAI;MACF,MAAM;QAAE9D,IAAI;QAAEM;MAAM,CAAC,GAAG,MAAMV,QAAQ,CAAC4C,GAAG,CAAC,wBAAwB,CAAC;MAEpE,IAAIlC,KAAK,EAAE,MAAMA,KAAK;MACtB,OAAO;QAAEN,IAAI;QAAEM,KAAK,EAAE;MAAK,CAAC;IAC9B,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdK,OAAO,CAACL,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;MAC3D,OAAO;QAAEN,IAAI,EAAE,IAAI;QAAEM;MAAM,CAAC;IAC9B;EACF,CAAC;EAED;EACA,MAAMyD,gBAAgBA,CAACC,QAAgB,EAAEC,iBAA0B,EAAE;IACnE,IAAI;MACF,MAAM;QAAEjE,IAAI;QAAEM;MAAM,CAAC,GAAG,MAAMV,QAAQ,CAAC4C,GAAG,CAAC,oBAAoB,EAAE;QAC/D0B,WAAW,EAAEF,QAAQ;QACrBG,oBAAoB,EAAEF,iBAAiB,IAAI;MAC7C,CAAC,CAAC;MAEF,IAAI3D,KAAK,EAAE,MAAMA,KAAK;MACtB,OAAO;QAAEqC,OAAO,EAAE,IAAI;QAAErC,KAAK,EAAE;MAAK,CAAC;IACvC,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdK,OAAO,CAACL,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrD,OAAO;QAAEqC,OAAO,EAAE,KAAK;QAAErC;MAAM,CAAC;IAClC;EACF,CAAC;EAED;EACA,MAAM8D,uBAAuBA,CAACC,WAM7B,EAAE;IACD,IAAI;MACF,MAAM;QAAErE,IAAI;QAAEM;MAAM,CAAC,GAAG,MAAMV,QAAQ,CAAC4C,GAAG,CAAC,4BAA4B,EAAE;QACvE8B,gBAAgB,EAAED,WAAW,CAACE,cAAc;QAC5CC,QAAQ,EAAEH,WAAW,CAACI,MAAM;QAC5BC,gBAAgB,EAAEL,WAAW,CAACM,cAAc,IAAI,IAAI;QACpDC,cAAc,EAAEP,WAAW,CAACQ,YAAY,IAAI,IAAI;QAChDC,eAAe,EAAET,WAAW,CAACU,aAAa,IAAI;MAChD,CAAC,CAAC;MAEF,IAAIzE,KAAK,EAAE,MAAMA,KAAK;MACtB,OAAO;QAAEN,IAAI;QAAEM,KAAK,EAAE;MAAK,CAAC;IAC9B,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdK,OAAO,CAACL,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/C,OAAO;QAAEN,IAAI,EAAE,IAAI;QAAEM;MAAM,CAAC;IAC9B;EACF,CAAC;EAED;EACA,MAAM0E,qBAAqBA,CAACC,SAAiB,EAAE;IAC7C,IAAI;MACF,MAAM;QAAEjF,IAAI;QAAEM;MAAM,CAAC,GAAG,MAAMV,QAAQ,CAAC4C,GAAG,CAAC,yBAAyB,EAAE;QACpE0C,YAAY,EAAED;MAChB,CAAC,CAAC;MAEF,IAAI3E,KAAK,EAAE,MAAMA,KAAK;MACtB,OAAO;QAAEqC,OAAO,EAAE,IAAI;QAAErC,KAAK,EAAE;MAAK,CAAC;IACvC,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdK,OAAO,CAACL,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD,OAAO;QAAEqC,OAAO,EAAE,KAAK;QAAErC;MAAM,CAAC;IAClC;EACF,CAAC;EAED;EACA,MAAM6E,mBAAmBA,CAAA,EAAG;IAC1B,IAAI;MACF,MAAM;QAAEnF,IAAI;QAAEM;MAAM,CAAC,GAAG,MAAMV,QAAQ,CACnCM,IAAI,CAAC,oBAAoB,CAAC,CAC1BC,MAAM,CAAC,GAAG,CAAC;MAEd,IAAIG,KAAK,EAAE,MAAMA,KAAK;MACtB,OAAO;QAAEN,IAAI;QAAEM,KAAK,EAAE;MAAK,CAAC;IAC9B,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdK,OAAO,CAACL,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;MAC3D,OAAO;QAAEN,IAAI,EAAE,IAAI;QAAEM;MAAM,CAAC;IAC9B;EACF,CAAC;EAED;EACA,MAAM8E,qBAAqBA,CAACpB,QAAgB,EAAE;IAC5C,IAAI;MACF,MAAM;QAAEhE,IAAI;QAAEM;MAAM,CAAC,GAAG,MAAMV,QAAQ,CACnCM,IAAI,CAAC,sBAAsB,CAAC,CAC5BC,MAAM,CAAC,GAAG,CAAC,CACXC,EAAE,CAAC,gBAAgB,EAAE4D,QAAQ,CAAC,CAC9B/C,KAAK,CAAC,YAAY,EAAE;QAAEC,SAAS,EAAE;MAAM,CAAC,CAAC;MAE5C,IAAIZ,KAAK,EAAE,MAAMA,KAAK;MACtB,OAAO;QAAEN,IAAI;QAAEM,KAAK,EAAE;MAAK,CAAC;IAC9B,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdK,OAAO,CAACL,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChD,OAAO;QAAEN,IAAI,EAAE,IAAI;QAAEM;MAAM,CAAC;IAC9B;EACF;AACF,CAAC;;AAED;AACA,OAAO,MAAM+E,YAAY,GAAG;EAC1B;EACA,MAAMC,YAAYA,CAACC,KAAa,EAAEC,MAAc,EAA2C;IACzF,IAAI;MACF7E,OAAO,CAACU,GAAG,CAAC,sBAAsBkE,KAAK,CAACpE,MAAM,mBAAmBqE,MAAM,EAAE,CAAC;;MAE1E;MACA,MAAM;QAAExF,IAAI,EAAE;UAAEyF;QAAK;MAAE,CAAC,GAAG,MAAM7F,QAAQ,CAAC8F,IAAI,CAACC,OAAO,CAAC,CAAC;MACxD,IAAI,CAACF,IAAI,EAAE;QACT,MAAM,IAAIG,KAAK,CAAC,wBAAwB,CAAC;MAC3C;MAEAjF,OAAO,CAACU,GAAG,CAAC,qBAAqB,EAAEoE,IAAI,CAACjF,EAAE,CAAC;MAE3C,MAAMqF,cAAc,GAAGN,KAAK,CAACO,GAAG,CAAC,OAAOC,IAAI,EAAEC,KAAK,KAAK;QACtD;QACA,MAAMC,OAAO,GAAGF,IAAI,CAACG,IAAI,CAACC,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC,CAAC;QAC1C,MAAMC,QAAQ,GAAG,GAAGb,MAAM,IAAIc,IAAI,CAACC,GAAG,CAAC,CAAC,IAAIP,KAAK,IAAIC,OAAO,EAAE;QAE9DtF,OAAO,CAACU,GAAG,CAAC,kBAAkB2E,KAAK,GAAG,CAAC,IAAIT,KAAK,CAACpE,MAAM,KAAKkF,QAAQ,EAAE,CAAC;QAEvE,MAAM;UAAErG,IAAI;UAAEM;QAAM,CAAC,GAAG,MAAMV,QAAQ,CAAC4G,OAAO,CAC3CtG,IAAI,CAAC,aAAa,CAAC,CACnBuG,MAAM,CAACJ,QAAQ,EAAEN,IAAI,EAAE;UACtBW,YAAY,EAAE,MAAM;UACpBC,MAAM,EAAE;QACV,CAAC,CAAC;QAEJ,IAAIrG,KAAK,EAAE;UACTK,OAAO,CAACL,KAAK,CAAC,oBAAoB+F,QAAQ,GAAG,EAAE/F,KAAK,CAAC;UACrD,MAAMA,KAAK;QACb;QAEAK,OAAO,CAACU,GAAG,CAAC,yBAAyBgF,QAAQ,GAAG,EAAErG,IAAI,CAAC;;QAEvD;QACA,MAAM;UAAEA,IAAI,EAAE4G;QAAQ,CAAC,GAAGhH,QAAQ,CAAC4G,OAAO,CACvCtG,IAAI,CAAC,aAAa,CAAC,CACnB2G,YAAY,CAACR,QAAQ,CAAC;QAEzB1F,OAAO,CAACU,GAAG,CAAC,kBAAkBgF,QAAQ,GAAG,EAAEO,OAAO,CAACE,SAAS,CAAC;QAC7D,OAAOF,OAAO,CAACE,SAAS;MAC1B,CAAC,CAAC;MAEF,MAAMC,IAAI,GAAG,MAAMC,OAAO,CAACC,GAAG,CAACpB,cAAc,CAAC;MAC9ClF,OAAO,CAACU,GAAG,CAAC,wBAAwB,EAAE0F,IAAI,CAAC;MAC3C,OAAO;QAAEA,IAAI;QAAEzG,KAAK,EAAE;MAAK,CAAC;IAC9B,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdK,OAAO,CAACL,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/C,OAAO;QAAEyG,IAAI,EAAE,EAAE;QAAEzG;MAAM,CAAC;IAC5B;EACF,CAAC;EAED;EACA,MAAM4G,YAAYA,CAACC,SAAmB,EAA6C;IACjF,IAAI;MACF;MACA,MAAMC,SAAS,GAAGD,SAAS,CAACrB,GAAG,CAACuB,GAAG,IAAI;QACrC,MAAMC,QAAQ,GAAGD,GAAG,CAAClB,KAAK,CAAC,GAAG,CAAC;QAC/B,MAAMoB,WAAW,GAAGD,QAAQ,CAACE,SAAS,CAACC,IAAI,IAAIA,IAAI,KAAK,aAAa,CAAC;QACtE,IAAIF,WAAW,KAAK,CAAC,CAAC,IAAIA,WAAW,GAAGD,QAAQ,CAACnG,MAAM,GAAG,CAAC,EAAE;UAC3D,OAAOmG,QAAQ,CAACI,KAAK,CAACH,WAAW,GAAG,CAAC,CAAC,CAACI,IAAI,CAAC,GAAG,CAAC;QAClD;QACA,OAAO,IAAI;MACb,CAAC,CAAC,CAACC,MAAM,CAACC,OAAO,CAAa;MAE9B,IAAIT,SAAS,CAACjG,MAAM,KAAK,CAAC,EAAE;QAC1B,OAAO;UAAEwB,OAAO,EAAE,IAAI;UAAErC,KAAK,EAAE;QAAK,CAAC;MACvC;MAEA,MAAM;QAAEA;MAAM,CAAC,GAAG,MAAMV,QAAQ,CAAC4G,OAAO,CACrCtG,IAAI,CAAC,aAAa,CAAC,CACnB4H,MAAM,CAACV,SAAS,CAAC;MAEpB,OAAO;QAAEzE,OAAO,EAAE,CAACrC,KAAK;QAAEA;MAAM,CAAC;IACnC,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdK,OAAO,CAACL,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9C,OAAO;QAAEqC,OAAO,EAAE,KAAK;QAAErC;MAAM,CAAC;IAClC;EACF,CAAC;EAED;EACAyH,oBAAoBA,CAACC,WAAmB,EAAEC,KAAc,EAAEC,MAAe,EAAU;IACjF;IACA;IACA,OAAOF,WAAW;EACpB,CAAC;EAED;EACAG,iBAAiBA,CAACpC,IAAU,EAAEqC,SAAiB,GAAG,EAAE,EAAsC;IACxF;IACA,IAAI,CAACrC,IAAI,CAACsC,IAAI,CAACC,UAAU,CAAC,QAAQ,CAAC,EAAE;MACnC,OAAO;QAAEC,KAAK,EAAE,KAAK;QAAEjI,KAAK,EAAE;MAAwB,CAAC;IACzD;;IAEA;IACA,MAAMkI,YAAY,GAAGJ,SAAS,GAAG,IAAI,GAAG,IAAI;IAC5C,IAAIrC,IAAI,CAAC0C,IAAI,GAAGD,YAAY,EAAE;MAC5B,OAAO;QAAED,KAAK,EAAE,KAAK;QAAEjI,KAAK,EAAE,gCAAgC8H,SAAS;MAAK,CAAC;IAC/E;;IAEA;IACA,MAAMM,cAAc,GAAG,CAAC,YAAY,EAAE,WAAW,EAAE,WAAW,EAAE,YAAY,CAAC;IAC7E,IAAI,CAACA,cAAc,CAACC,QAAQ,CAAC5C,IAAI,CAACsC,IAAI,CAAC,EAAE;MACvC,OAAO;QAAEE,KAAK,EAAE,KAAK;QAAEjI,KAAK,EAAE;MAAqC,CAAC;IACtE;IAEA,OAAO;MAAEiI,KAAK,EAAE;IAAK,CAAC;EACxB;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}